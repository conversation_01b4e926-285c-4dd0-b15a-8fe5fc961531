#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إدارة الموظفين - الإصدار المحسن والمُصحح
حل جميع مشاكل البرنامج
"""

import os
import sys
import sqlite3
import json
import webbrowser
import threading
import time
from datetime import datetime, timedelta

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_and_install_requirements():
    """فحص وتثبيت المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_packages = ['flask', 'flask-cors']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'flask-cors':
                import flask_cors
            else:
                __import__(package)
            print(f"✅ {package} - متاح")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - غير متاح")
    
    if missing_packages:
        print(f"\n📦 تثبيت المكتبات المفقودة: {', '.join(missing_packages)}")
        try:
            import subprocess
            for package in missing_packages:
                print(f"تثبيت {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ تم تثبيت {package}")
        except Exception as e:
            print(f"❌ خطأ في التثبيت: {e}")
            print("💡 قم بتثبيت المكتبات يدوياً:")
            print("pip install flask flask-cors")
            return False
    
    return True

def create_simple_app():
    """إنشاء تطبيق Flask مبسط"""
    try:
        from flask import Flask, render_template, jsonify, request
        from flask_cors import CORS
        
        app = Flask(__name__)
        CORS(app)
        app.secret_key = 'employee_management_2024'
        
        # إعداد قاعدة البيانات
        DATABASE = 'employees_fixed.db'
        
        def init_database():
            """تهيئة قاعدة البيانات"""
            conn = sqlite3.connect(DATABASE)
            cursor = conn.cursor()
            
            # جدول الموظفين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    company_number TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    job_title TEXT NOT NULL,
                    work_location TEXT NOT NULL,
                    start_date TEXT NOT NULL,
                    address TEXT,
                    birth_date TEXT,
                    mobile TEXT,
                    created_at TEXT NOT NULL
                )
            ''')
            
            # جدول الدورات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS courses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER,
                    title TEXT NOT NULL,
                    type TEXT NOT NULL,
                    period TEXT NOT NULL,
                    location TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # جدول الإجازات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS leaves (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER,
                    leave_date TEXT NOT NULL,
                    leave_duration TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # جدول الإيفادات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS delegations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER,
                    destination TEXT NOT NULL,
                    country TEXT NOT NULL,
                    purpose TEXT NOT NULL,
                    start_date TEXT NOT NULL,
                    end_date TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # جدول الكتب
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS books (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    author TEXT,
                    category TEXT,
                    publisher TEXT,
                    publication_year INTEGER,
                    pages INTEGER,
                    language TEXT DEFAULT 'العربية',
                    location TEXT,
                    status TEXT DEFAULT 'متاح',
                    created_at TEXT NOT NULL
                )
            ''')
            
            # جدول التنبيهات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    type TEXT NOT NULL,
                    date TEXT NOT NULL,
                    read INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL
                )
            ''')
            
            # إدراج بيانات تجريبية
            cursor.execute('SELECT COUNT(*) FROM employees')
            if cursor.fetchone()[0] == 0:
                sample_employees = [
                    ('EMP001', 'أحمد محمد علي', 'مطور برمجيات', 'الرياض', '2023-01-15', 'الرياض - حي النرجس', '1990-05-20', '0501234567'),
                    ('EMP002', 'فاطمة خالد السعد', 'محاسبة', 'جدة', '2023-02-01', 'جدة - حي الزهراء', '1988-08-12', '0509876543'),
                    ('EMP003', 'محمد عبدالله القحطاني', 'مدير مشروع', 'الدمام', '2023-03-10', 'الدمام - حي الشاطئ', '1985-12-03', '0551122334')
                ]
                
                for emp in sample_employees:
                    cursor.execute('''
                        INSERT INTO employees (company_number, full_name, job_title, work_location, start_date, address, birth_date, mobile, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', emp + (datetime.now().isoformat(),))
            
            conn.commit()
            conn.close()
            print("✅ تم إعداد قاعدة البيانات")
        
        # تهيئة قاعدة البيانات
        init_database()
        
        # الصفحة الرئيسية
        @app.route('/')
        def index():
            return render_template('index.html')
        
        # API للإحصائيات
        @app.route('/api/statistics')
        def get_statistics():
            conn = sqlite3.connect(DATABASE)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM employees')
            employees_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM courses')
            courses_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM leaves')
            leaves_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM delegations')
            delegations_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM books')
            books_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM notifications WHERE read = 0')
            unread_notifications = cursor.fetchone()[0]
            
            conn.close()
            
            return jsonify({
                'employees_count': employees_count,
                'courses_count': courses_count,
                'leaves_count': leaves_count,
                'delegations_count': delegations_count,
                'books_count': books_count,
                'borrowed_books': 0,
                'unread_notifications': unread_notifications
            })
        
        # API للموظفين
        @app.route('/api/employees')
        def get_employees():
            conn = sqlite3.connect(DATABASE)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM employees ORDER BY id')
            rows = cursor.fetchall()
            
            employees = []
            for i, row in enumerate(rows, 1):
                employees.append({
                    'sequence': i,
                    'id': row[0],
                    'company_number': row[1],
                    'full_name': row[2],
                    'job_title': row[3],
                    'work_location': row[4],
                    'start_date': row[5],
                    'address': row[6],
                    'birth_date': row[7],
                    'mobile': row[8]
                })
            
            conn.close()
            return jsonify(employees)
        
        # APIs أخرى (مبسطة)
        @app.route('/api/courses')
        def get_courses():
            return jsonify([])
        
        @app.route('/api/leaves')
        def get_leaves():
            return jsonify([])
        
        @app.route('/api/delegations')
        def get_delegations():
            return jsonify([])
        
        @app.route('/api/books')
        def get_books():
            return jsonify([])
        
        @app.route('/api/notifications')
        def get_notifications():
            return jsonify([])
        
        @app.route('/api/service')
        def get_service():
            conn = sqlite3.connect(DATABASE)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, company_number, full_name, job_title, start_date
                FROM employees ORDER BY id
            ''')
            rows = cursor.fetchall()
            
            service_data = []
            for i, row in enumerate(rows, 1):
                # حساب مدة الخدمة
                start_date = datetime.fromisoformat(row[4])
                now = datetime.now()
                delta = now - start_date
                years = delta.days // 365
                months = (delta.days % 365) // 30
                days = (delta.days % 365) % 30
                
                duration = f"{years} سنة و {months} شهر و {days} يوم"
                
                service_data.append({
                    'sequence': i,
                    'id': row[0],
                    'company_number': row[1],
                    'full_name': row[2],
                    'job_title': row[3],
                    'service_duration': duration
                })
            
            conn.close()
            return jsonify(service_data)
        
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        return None

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح")
    except:
        print("⚠️ لم يتم فتح المتصفح تلقائياً")
        print("🌐 افتح المتصفح يدوياً على: http://localhost:5000")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🚀 نظام إدارة الموظفين - الإصدار المُصحح")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_and_install_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء التطبيق
    app = create_simple_app()
    if not app:
        input("اضغط Enter للخروج...")
        return
    
    print("✅ تم إعداد التطبيق بنجاح")
    print("🌐 الخادم متاح على: http://localhost:5000")
    print("📱 سيتم فتح المتصفح تلقائياً...")
    print("💡 للإيقاف: اضغط Ctrl+C")
    print("=" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # تشغيل الخادم
        app.run(debug=False, host='127.0.0.1', port=5000, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
    finally:
        print("👋 تم إغلاق النظام")

if __name__ == '__main__':
    main()
