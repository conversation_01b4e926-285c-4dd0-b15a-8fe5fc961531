/* نظام إدارة الموظفين - التصميم المخصص */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&family=Cairo:wght@300;400;600;700;900&family=Amiri:wght@400;700&display=swap');

/* المتغيرات الأساسية */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* الإعدادات الأساسية */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', 'Cairo', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f7fa;
    direction: rtl;
    text-align: right;
}

/* الثيم الداكن */
[data-theme="dark"] {
    --light-color: #2d3748;
    --dark-color: #e2e8f0;
    background-color: #1a202c;
    color: var(--dark-color);
}

[data-theme="dark"] .bg-light {
    background-color: var(--light-color) !important;
}

[data-theme="dark"] .card {
    background-color: #2d3748;
    border-color: #4a5568;
    color: var(--dark-color);
}

[data-theme="dark"] .table {
    --bs-table-bg: #2d3748;
    --bs-table-color: #e2e8f0;
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: #374151;
}

/* الخطوط */
.font-tajawal { font-family: 'Tajawal', sans-serif; }
.font-cairo { font-family: 'Cairo', sans-serif; }
.font-amiri { font-family: 'Amiri', serif; }

/* أحجام الخطوط */
.font-size-small { font-size: 0.875rem; }
.font-size-medium { font-size: 1rem; }
.font-size-large { font-size: 1.125rem; }

/* الرسالة الترحيبية */
.welcome-message {
    margin-bottom: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius);
    color: white;
    box-shadow: var(--box-shadow);
}

.welcome-message h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.welcome-message p {
    font-size: 0.9rem;
    margin-bottom: 0;
}

.animated-welcome {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* بطاقات الإحصائيات */
.stat-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    overflow: hidden;
}

.stat-card .card-body {
    padding: 1rem;
}

.stat-card h2 {
    font-size: 1.5rem;
    margin-bottom: 0;
}

.stat-card h6 {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 1.8rem;
    opacity: 0.8;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.bg-gradient-purple {
    background: linear-gradient(45deg, #6f42c1, #5a2d91);
}

.text-purple {
    color: #6f42c1 !important;
}

.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-purple:hover {
    background-color: #5a2d91;
    border-color: #5a2d91;
    color: white;
}

/* تحسينات النماذج المنبثقة */
.modal-header {
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 1.5rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
    gap: 0.5rem;
}

.modal-footer .btn {
    min-width: 80px;
}

.modal-footer .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.modal-footer .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* تحسين مظهر النماذج */
.modal-body {
    padding: 1.5rem;
}

.modal-body .form-label {
    color: #495057;
    margin-bottom: 0.5rem;
}

.modal-body .form-control:focus,
.modal-body .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* أزرار الإجراءات السريعة */
.btn-outline-purple {
    border-color: #6f42c1;
    color: #6f42c1;
}

.btn-outline-purple:hover {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

/* تحسينات الأزرار */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-group .btn:not(:last-child) {
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

/* تحسينات البحث */
.input-group .form-control {
    border-right: none;
}

.input-group .input-group-text {
    background-color: #f8f9fa;
    border-left: none;
}

/* تحسينات الجداول */
.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.table .btn-group {
    white-space: nowrap;
}

/* تحسينات التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* التبويبات المخصصة */
.nav-tabs-custom {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0;
}

.nav-tabs-custom .nav-link {
    border: none;
    border-radius: 0;
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    font-size: 0.85rem;
    color: #6c757d;
    transition: var(--transition);
    position: relative;
    margin-left: 0.1rem;
    margin-right: 0.1rem;
}

.nav-tabs-custom .nav-link i {
    font-size: 0.9rem;
}

.nav-tabs-custom .nav-link:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary-color);
}

.nav-tabs-custom .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.nav-tabs-custom .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--primary-color);
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    margin-bottom: 0;
    font-size: 0.85rem;
}

.table thead th {
    border-bottom: 1px solid #dee2e6;
    font-weight: 700;
    font-size: 0.75rem;
    padding: 0.5rem;
    letter-spacing: 0.3px;
}

.table tbody td {
    padding: 0.5rem;
    vertical-align: middle;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    font-size: 0.85rem;
    transition: var(--transition);
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-group .btn {
    margin-left: 0.1rem;
    margin-right: 0.1rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
}

/* النماذج المنبثقة */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* حقول الإدخال */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-bottom: 3px solid var(--primary-color);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

/* الرسائل والتنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* التحسينات الإضافية */
.text-muted {
    color: #6c757d !important;
}

.fw-bold {
    font-weight: 700 !important;
}

.shadow-sm {
    box-shadow: var(--box-shadow) !important;
}

/* تحسينات المساحات */
.container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

.card-body {
    padding: 1rem;
}

.card-header {
    padding: 0.75rem 1rem;
}

.mb-4 {
    margin-bottom: 1rem !important;
}

.mb-3 {
    margin-bottom: 0.75rem !important;
}

/* تحسينات عناوين الأقسام */
h4 {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
}

h5 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .welcome-message {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .welcome-message h1 {
        font-size: 1.25rem;
    }

    .nav-tabs-custom .nav-link {
        padding: 0.5rem 0.5rem;
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin: 0.1rem 0;
    }

    .stat-card {
        margin-bottom: 0.75rem;
    }

    .table {
        font-size: 0.75rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.375rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .navbar,
    .btn,
    .modal,
    .welcome-message {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }

    .table {
        font-size: 0.875rem;
    }

    body {
        background-color: white;
        color: black;
    }
}

/* تحسينات إضافية للثيم الداكن */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: #374151;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: #374151;
    border-color: var(--primary-color);
    color: #e2e8f0;
}

[data-theme="dark"] .modal-content {
    background-color: #2d3748;
    color: #e2e8f0;
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* تحسينات الأيقونات */
.bi {
    vertical-align: -0.125em;
}

/* تحسينات الشارات */
.badge {
    font-size: 0.75em;
    font-weight: 600;
}

/* تحسينات القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
}

.dropdown-item {
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary-color);
}

/* تحسينات لوحة التحكم */
.stat-card .card-footer {
    padding: 0.5rem 1rem;
}

.stat-card .card-footer .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* تحسينات النشاط الأخير */
.timeline {
    max-height: 400px;
    overflow-y: auto;
}

.timeline::-webkit-scrollbar {
    width: 6px;
}

.timeline::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.timeline::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.timeline::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تحسينات الإجراءات السريعة */
.quick-action-btn {
    min-height: 120px;
    transition: var(--transition);
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* تحسينات بطاقات الإحصائيات */
.stat-card .card-body {
    position: relative;
    overflow: hidden;
}

.stat-card .card-body::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: var(--transition);
    transform: scale(0);
}

.stat-card:hover .card-body::before {
    transform: scale(2);
}

.stat-card .stat-icon {
    position: relative;
    z-index: 2;
}

.stat-card .card-title,
.stat-card h2,
.stat-card small {
    position: relative;
    z-index: 2;
}

/* تحسينات الثيم الداكن للوحة التحكم */
[data-theme="dark"] .timeline::-webkit-scrollbar-track {
    background: #374151;
}

[data-theme="dark"] .timeline::-webkit-scrollbar-thumb {
    background: #6b7280;
}

[data-theme="dark"] .timeline::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* تأثيرات إضافية */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
