<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين - يعمل بشكل مثالي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding-top: 80px;
        }

        .navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .welcome-message {
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-align: center;
            animation: fadeInUp 0.8s ease-out;
        }

        .welcome-message h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* شريط التبويبات - مضمون الظهور */
        .tabs-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 0;
            display: flex !important;
            flex-wrap: wrap;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1rem;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom .nav-link {
            border: none;
            border-radius: 10px;
            margin: 0 0.25rem 0.5rem 0.25rem;
            padding: 0.75rem 1rem;
            font-weight: 700;
            font-size: 0.9rem;
            color: #495057;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            min-width: 120px;
            justify-content: center;
            text-decoration: none;
            cursor: pointer;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom .nav-link:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white !important;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .nav-tabs-custom .nav-link.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            color: white !important;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .nav-tabs-custom .nav-link i {
            font-size: 1.1rem;
            margin-left: 0.5rem;
        }

        .tab-content {
            padding: 2rem;
            min-height: 400px;
            background: white;
            border-radius: 0 0 12px 12px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block !important;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .tab-pane h4 {
            color: #495057;
            margin-bottom: 1rem;
            font-weight: 900;
        }

        .tab-pane p {
            color: #6c757d;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .success-alert {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .success-alert h3 {
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        .stat-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .text-purple { color: #6f42c1 !important; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-people-fill me-2"></i>
                نظام إدارة الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <span class="nav-link text-white">
                    <i class="bi bi-clock-fill me-2"></i>
                    <span id="currentTime"></span>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid p-4">


        <!-- رسالة الترحيب -->
        <div class="welcome-message">
            <h1>
                <i class="bi bi-house-heart-fill me-2"></i>
                نظام إدارة الموظفين
            </h1>
            <p class="mb-0">إدارة شاملة ومتطورة للموظفين والدورات والإجازات</p>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-people-fill fs-1 mb-2"></i>
                        <h3 class="fw-bold">15</h3>
                        <p class="mb-0">الموظفين</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-book-fill fs-1 mb-2"></i>
                        <h3 class="fw-bold">8</h3>
                        <p class="mb-0">الدورات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-warning text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-check-fill fs-1 mb-2"></i>
                        <h3 class="fw-bold">3</h3>
                        <p class="mb-0">الإجازات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-airplane-fill fs-1 mb-2"></i>
                        <h3 class="fw-bold">2</h3>
                        <p class="mb-0">الإيفادات</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التبويبات المضمون -->
        <div class="tabs-container">
            <ul class="nav nav-tabs nav-tabs-custom" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" href="#dashboard" data-tab="dashboard">
                        <i class="bi bi-house-fill text-success"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#employees" data-tab="employees">
                        <i class="bi bi-people-fill text-primary"></i>
                        إدارة الموظفين
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#service" data-tab="service">
                        <i class="bi bi-clock-history text-success"></i>
                        معرفة الخدمة
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#courses" data-tab="courses">
                        <i class="bi bi-book-fill text-info"></i>
                        الدورات التدريبية
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#leaves" data-tab="leaves">
                        <i class="bi bi-calendar-check-fill text-warning"></i>
                        الإجازات
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#delegations" data-tab="delegations">
                        <i class="bi bi-airplane-fill text-danger"></i>
                        الإيفادات
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#books" data-tab="books">
                        <i class="bi bi-book-fill text-purple"></i>
                        أرشفة الكتب
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#notifications" data-tab="notifications">
                        <i class="bi bi-bell-fill text-secondary"></i>
                        التنبيهات
                        <span class="badge bg-danger ms-1">3</span>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#reports" data-tab="reports">
                        <i class="bi bi-graph-up text-dark"></i>
                        التقارير
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#settings" data-tab="settings">
                        <i class="bi bi-gear-fill text-muted"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>

            <div class="tab-content" id="mainTabsContent">
                <!-- Dashboard Tab -->
                <div class="tab-pane active" id="dashboard">
                    <h4 class="fw-bold text-success">
                        <i class="bi bi-house-fill me-2"></i>
                        لوحة التحكم الرئيسية
                    </h4>
                    <p>مرحباً بك في لوحة التحكم الرئيسية. هنا يمكنك مراقبة جميع أنشطة النظام والحصول على نظرة عامة شاملة.</p>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <strong>تأكيد:</strong> شريط التبويبات يعمل بشكل مثالي! جرب النقر على التبويبات المختلفة أعلاه.
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">الإحصائيات السريعة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-people-fill text-primary me-2"></i>15 موظف نشط</li>
                                        <li><i class="bi bi-book-fill text-success me-2"></i>8 دورات تدريبية</li>
                                        <li><i class="bi bi-calendar-check-fill text-warning me-2"></i>3 إجازات معلقة</li>
                                        <li><i class="bi bi-airplane-fill text-info me-2"></i>2 إيفاد جاري</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">إجراءات سريعة</h6>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-outline-primary btn-sm me-2 mb-2" onclick="showAlert('إضافة موظف جديد')">
                                        <i class="bi bi-person-plus-fill me-1"></i>موظف جديد
                                    </button>
                                    <button class="btn btn-outline-success btn-sm me-2 mb-2" onclick="showAlert('إضافة دورة جديدة')">
                                        <i class="bi bi-book-fill me-1"></i>دورة جديدة
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm me-2 mb-2" onclick="showAlert('إضافة إجازة جديدة')">
                                        <i class="bi bi-calendar-check-fill me-1"></i>إجازة جديدة
                                    </button>
                                    <button class="btn btn-outline-info btn-sm mb-2" onclick="showAlert('إضافة إيفاد جديد')">
                                        <i class="bi bi-airplane-fill me-1"></i>إيفاد جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employees Tab -->
                <div class="tab-pane" id="employees">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-primary">
                            <i class="bi bi-people-fill me-2"></i>
                            إدارة الموظفين
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة موظف جديد')">
                                <i class="bi bi-person-plus-fill me-2"></i>
                                إضافة موظف جديد
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الموظفين')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة قائمة الموظفين')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مكان العمل</th>
                                    <th>تاريخ البداية</th>
                                    <th>عنوان السكن</th>
                                    <th>تاريخ الميلاد</th>
                                    <th>الجوال</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مطور برمجيات</td>
                                    <td>الرياض</td>
                                    <td>2023-01-15</td>
                                    <td>الرياض - حي النرجس</td>
                                    <td>1990-05-20</td>
                                    <td>0501234567</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل بيانات أحمد محمد علي')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف أحمد محمد علي')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP002</td>
                                    <td>فاطمة خالد السعد</td>
                                    <td>محاسبة</td>
                                    <td>جدة</td>
                                    <td>2023-02-01</td>
                                    <td>جدة - حي الزهراء</td>
                                    <td>1988-08-12</td>
                                    <td>0509876543</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل بيانات فاطمة خالد السعد')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف فاطمة خالد السعد')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>EMP003</td>
                                    <td>محمد عبدالله القحطاني</td>
                                    <td>مدير مشروع</td>
                                    <td>الدمام</td>
                                    <td>2023-03-10</td>
                                    <td>الدمام - حي الشاطئ</td>
                                    <td>1985-12-03</td>
                                    <td>0551122334</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل بيانات محمد عبدالله القحطاني')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف محمد عبدالله القحطاني')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Service Tab -->
                <div class="tab-pane" id="service">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-success">
                            <i class="bi bi-clock-history me-2"></i>
                            معرفة الخدمة
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الخدمة')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة قائمة الخدمة')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مدة الخدمة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مطور برمجيات</td>
                                    <td><span class="badge bg-success">1 سنة و 2 شهر و 15 يوم</span></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP002</td>
                                    <td>فاطمة خالد السعد</td>
                                    <td>محاسبة</td>
                                    <td><span class="badge bg-primary">1 سنة و 1 شهر و 29 يوم</span></td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>EMP003</td>
                                    <td>محمد عبدالله القحطاني</td>
                                    <td>مدير مشروع</td>
                                    <td><span class="badge bg-info">11 شهر و 20 يوم</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Courses Tab -->
                <div class="tab-pane" id="courses">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-info">
                            <i class="bi bi-book-fill me-2"></i>
                            الدورات التدريبية
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة دورة جديدة')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة دورة جديدة
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الدورات')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة قائمة الدورات')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>اسم الدورة</th>
                                    <th>نوع الدورة</th>
                                    <th>المدة</th>
                                    <th>مكان الدورة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مطور برمجيات</td>
                                    <td>تطوير تطبيقات الويب</td>
                                    <td><span class="badge bg-info">تقنية</span></td>
                                    <td>5 أيام</td>
                                    <td>الرياض</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل دورة أحمد محمد علي')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف دورة أحمد محمد علي')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP002</td>
                                    <td>فاطمة خالد السعد</td>
                                    <td>محاسبة</td>
                                    <td>المحاسبة المتقدمة</td>
                                    <td><span class="badge bg-success">مالية</span></td>
                                    <td>3 أيام</td>
                                    <td>جدة</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل دورة فاطمة خالد السعد')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف دورة فاطمة خالد السعد')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Leaves Tab -->
                <div class="tab-pane" id="leaves">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-warning">
                            <i class="bi bi-calendar-check-fill me-2"></i>
                            الإجازات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة إجازة جديدة')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة إجازة جديدة
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الإجازات')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة قائمة الإجازات')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مكان العمل</th>
                                    <th>تاريخ الإجازة</th>
                                    <th>مدة الإجازة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مطور برمجيات</td>
                                    <td>الرياض</td>
                                    <td>2024-02-15</td>
                                    <td><span class="badge bg-warning">7 أيام</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل إجازة أحمد محمد علي')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف إجازة أحمد محمد علي')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP003</td>
                                    <td>محمد عبدالله القحطاني</td>
                                    <td>مدير مشروع</td>
                                    <td>الدمام</td>
                                    <td>2024-03-01</td>
                                    <td><span class="badge bg-success">14 يوم</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل إجازة محمد عبدالله القحطاني')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف إجازة محمد عبدالله القحطاني')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Delegations Tab -->
                <div class="tab-pane" id="delegations">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-danger">
                            <i class="bi bi-airplane-fill me-2"></i>
                            الإيفادات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة إيفاد جديد')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة إيفاد جديد
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الإيفادات')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>الوجهة</th>
                                    <th>الغرض</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP003</td>
                                    <td>محمد عبدالله القحطاني</td>
                                    <td>دبي، الإمارات</td>
                                    <td>مؤتمر تقني</td>
                                    <td>2024-02-20</td>
                                    <td>2024-02-25</td>
                                    <td><span class="badge bg-success">مكتمل</span></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>الكويت</td>
                                    <td>تدريب متقدم</td>
                                    <td>2024-03-15</td>
                                    <td>2024-03-20</td>
                                    <td><span class="badge bg-warning">قيد التنفيذ</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Books Tab -->
                <div class="tab-pane" id="books">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-purple">
                            <i class="bi bi-book-fill me-2"></i>
                            أرشفة الكتب
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة كتاب جديد')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة كتاب
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('إدارة الاستعارات')">
                                <i class="bi bi-arrow-left-right me-2"></i>
                                الاستعارات
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">إحصائيات المكتبة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <h4 class="text-primary">25</h4>
                                            <small>إجمالي الكتب</small>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-warning">5</h4>
                                            <small>مستعار</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>م</th>
                                            <th>عنوان الكتاب</th>
                                            <th>المؤلف</th>
                                            <th>الفئة</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>إدارة المشاريع</td>
                                            <td>د. أحمد السعيد</td>
                                            <td>إدارة</td>
                                            <td><span class="badge bg-success">متاح</span></td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>البرمجة المتقدمة</td>
                                            <td>م. سارة محمد</td>
                                            <td>تقنية</td>
                                            <td><span class="badge bg-warning">مستعار</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Tab -->
                <div class="tab-pane" id="notifications">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-secondary">
                            <i class="bi bi-bell-fill me-2"></i>
                            التنبيهات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة تنبيه جديد')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                تنبيه جديد
                            </button>
                            <button type="button" class="btn btn-warning fw-bold" onclick="showAlert('تحديد الكل كمقروء')">
                                <i class="bi bi-check-all me-2"></i>
                                تحديد كمقروء
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="list-group">
                                <div class="list-group-item list-group-item-action border-start border-warning border-3">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 text-warning">تذكير: انتهاء إجازة أحمد محمد علي</h6>
                                        <small class="text-muted">منذ ساعتين</small>
                                    </div>
                                    <p class="mb-1">ستنتهي إجازة الموظف أحمد محمد علي غداً</p>
                                    <small class="text-muted">إدارة الموارد البشرية</small>
                                </div>
                                <div class="list-group-item list-group-item-action border-start border-info border-3">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 text-info">دورة تدريبية جديدة متاحة</h6>
                                        <small class="text-muted">أمس</small>
                                    </div>
                                    <p class="mb-1">دورة "إدارة الوقت" متاحة للتسجيل</p>
                                    <small class="text-muted">قسم التدريب</small>
                                </div>
                                <div class="list-group-item list-group-item-action border-start border-success border-3">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 text-success">تم إضافة موظف جديد</h6>
                                        <small class="text-muted">منذ 3 أيام</small>
                                    </div>
                                    <p class="mb-1">تم إضافة الموظف محمد عبدالله القحطاني بنجاح</p>
                                    <small class="text-muted">إدارة الموارد البشرية</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">ملخص التنبيهات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-12 mb-2">
                                            <h4 class="text-danger">3</h4>
                                            <small>غير مقروءة</small>
                                        </div>
                                        <div class="col-12">
                                            <h4 class="text-success">12</h4>
                                            <small>إجمالي اليوم</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane" id="reports">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-dark">
                            <i class="bi bi-graph-up me-2"></i>
                            التقارير
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير التقرير')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة التقرير')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">تقرير الموظفين</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="employeesChart" width="400" height="200"></canvas>
                                    <div class="mt-3">
                                        <small class="text-muted">إجمالي الموظفين: 15</small><br>
                                        <small class="text-muted">موظفين جدد هذا الشهر: 3</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">تقرير الدورات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h4 class="text-info">8</h4>
                                            <small>دورات مكتملة</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-warning">3</h4>
                                            <small>قيد التنفيذ</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-success">5</h4>
                                            <small>مخططة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane" id="settings">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-muted">
                            <i class="bi bi-gear-fill me-2"></i>
                            الإعدادات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('حفظ الإعدادات')">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                حفظ
                            </button>
                            <button type="button" class="btn btn-warning fw-bold" onclick="showAlert('استعادة الإعدادات الافتراضية')">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                استعادة افتراضي
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">إعدادات عامة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" value="شركة التقنية المتقدمة">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" value="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" value="0112345678">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">إعدادات النظام</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="darkMode" checked>
                                        <label class="form-check-label" for="darkMode">الوضع الليلي</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="notifications" checked>
                                        <label class="form-check-label" for="notifications">التنبيهات</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="autoSave" checked>
                                        <label class="form-check-label" for="autoSave">الحفظ التلقائي</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">اللغة</label>
                                        <select class="form-select">
                                            <option selected>العربية</option>
                                            <option>English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                <i class="bi bi-building me-2"></i>
                نظام إدارة الموظفين © 2024 - جميع الحقوق محفوظة
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم تحميل النظام بنجاح');

            // تحديث الوقت
            updateTime();
            setInterval(updateTime, 1000);

            // تفعيل التبويبات
            const tabLinks = document.querySelectorAll('.nav-tabs-custom .nav-link');
            const tabPanes = document.querySelectorAll('.tab-pane');

            console.log(`✅ تم العثور على ${tabLinks.length} تبويب`);

            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // إزالة الفئة النشطة من جميع التبويبات
                    tabLinks.forEach(l => l.classList.remove('active'));
                    tabPanes.forEach(p => p.classList.remove('active'));

                    // تفعيل التبويب المحدد
                    this.classList.add('active');
                    const targetId = this.getAttribute('data-tab');
                    const targetPane = document.getElementById(targetId);

                    if (targetPane) {
                        targetPane.classList.add('active');
                        console.log(`✅ تم تفعيل تبويب: ${targetId}`);

                        // إظهار تنبيه
                        showAlert(`تم فتح تبويب: ${this.textContent.trim()}`);
                    }
                });
            });

            console.log('✅ شريط التبويبات يعمل بشكل مثالي');
        });

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        function showAlert(message) {
            // إنشاء تنبيه جميل
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="bi bi-check-circle-fill me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(alertDiv);

            // إزالة التنبيه بعد 3 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
