<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين - يعمل بشكل مثالي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding-top: 80px;
        }

        .navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .welcome-message {
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-align: center;
            animation: fadeInUp 0.8s ease-out;
        }

        .welcome-message h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* شريط التبويبات - مضمون الظهور */
        .tabs-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 0;
            display: flex !important;
            flex-wrap: wrap;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1rem;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom .nav-link {
            border: none;
            border-radius: 10px;
            margin: 0 0.25rem 0.5rem 0.25rem;
            padding: 0.75rem 1rem;
            font-weight: 700;
            font-size: 0.9rem;
            color: #495057;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            min-width: 120px;
            justify-content: center;
            text-decoration: none;
            cursor: pointer;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom .nav-link:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white !important;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .nav-tabs-custom .nav-link.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            color: white !important;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .nav-tabs-custom .nav-link i {
            font-size: 1.1rem;
            margin-left: 0.5rem;
        }

        .tab-content {
            padding: 2rem;
            min-height: 400px;
            background: white;
            border-radius: 0 0 12px 12px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block !important;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .tab-pane h4 {
            color: #495057;
            margin-bottom: 1rem;
            font-weight: 900;
        }

        .tab-pane p {
            color: #6c757d;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .success-alert {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .success-alert h3 {
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        .stat-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            overflow: hidden;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--card-bg) 0%, var(--card-bg-end) 100%);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
        }

        .stat-card.bg-gradient-primary {
            --card-bg: #007bff;
            --card-bg-end: #0056b3;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .stat-card.bg-gradient-success {
            --card-bg: #28a745;
            --card-bg-end: #1e7e34;
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }

        .stat-card.bg-gradient-warning {
            --card-bg: #ff8c00;
            --card-bg-end: #ff6b00;
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
        }

        .stat-card.bg-gradient-info {
            --card-bg: #17a2b8;
            --card-bg-end: #138496;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .stat-card .card-body {
            padding: 1.5rem;
            position: relative;
        }

        .stat-card .card-body::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }

        .stat-card h3 {
            font-size: 2.2rem;
            font-weight: 900;
            margin-bottom: 0.25rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .stat-card p {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0;
            opacity: 0.95;
        }

        .text-purple { color: #6f42c1 !important; }

        /* إعدادات الطباعة */
        @media print {
            /* إخفاء جميع العناصر عند الطباعة */
            * {
                visibility: hidden !important;
            }

            /* إظهار جدول الموظفين فقط */
            #employees.active,
            #employees.active *,
            #employees .print-header,
            #employees .print-header *,
            #employees .table-responsive,
            #employees .table-responsive *,
            #employees .table,
            #employees .table * {
                visibility: visible !important;
            }

            /* إخفاء العناصر غير المرغوبة حتى في جدول الموظفين */
            .navbar,
            .welcome-message,
            .stat-card,
            .nav-tabs-custom,
            .btn,
            .actions-column,
            .no-print,
            .tab-content > .tab-pane:not(#employees) {
                display: none !important;
                visibility: hidden !important;
            }

            /* تنسيق الجدول للطباعة */
            .table-responsive {
                overflow: visible !important;
                position: static !important;
            }

            .table {
                border-collapse: collapse !important;
                width: 100% !important;
                font-size: 12px !important;
                margin: 0 !important;
            }

            .table th,
            .table td {
                border: 1px solid #000 !important;
                padding: 8px !important;
                text-align: center !important;
                background: white !important;
            }

            .table thead th {
                background-color: #f8f9fa !important;
                font-weight: bold !important;
                color: #000 !important;
            }

            /* عنوان الطباعة */
            .print-header {
                display: block !important;
                text-align: center !important;
                margin-bottom: 20px !important;
                font-size: 18px !important;
                font-weight: bold !important;
                position: static !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
            }

            .print-date {
                display: block !important;
                text-align: left !important;
                margin-bottom: 10px !important;
                font-size: 12px !important;
            }

            /* تحسين تخطيط الطباعة */
            body {
                background: white !important;
                color: black !important;
                font-family: Arial, sans-serif !important;
                margin: 0 !important;
                padding: 20px !important;
            }

            .container-fluid {
                padding: 0 !important;
                margin: 0 !important;
                width: 100% !important;
            }

            /* إخفاء التبويبات غير النشطة */
            .tab-pane:not(.active) {
                display: none !important;
                visibility: hidden !important;
            }

            /* التأكد من ظهور التبويب النشط فقط */
            .tab-pane.active {
                display: block !important;
                visibility: visible !important;
                position: static !important;
            }

            /* إخفاء أزرار الإجراءات */
            .actions-column {
                display: none !important;
                visibility: hidden !important;
            }

            /* تحسين عرض النص */
            h2, h3, h4, h5, h6 {
                color: #000 !important;
                margin: 10px 0 !important;
            }

            /* إزالة الظلال والتأثيرات */
            .card, .table, .btn {
                box-shadow: none !important;
                border-radius: 0 !important;
            }
        }

        /* تحسينات تبويب الإعدادات */
        .settings-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
        }

        .settings-icon {
            background: linear-gradient(135deg, #0d6efd, #0056b3);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
        }

        .settings-icon i {
            color: white !important;
            font-size: 2rem;
        }

        .settings-nav .nav-pills .nav-link {
            border-radius: 12px;
            padding: 12px 20px;
            margin: 0 5px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-weight: 600;
        }

        .settings-nav .nav-pills .nav-link:hover {
            background-color: rgba(13, 110, 253, 0.1);
            border-color: rgba(13, 110, 253, 0.3);
            transform: translateY(-2px);
        }

        .settings-nav .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #0d6efd, #0056b3);
            border-color: #0d6efd;
            box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
        }

        .settings-section {
            animation: fadeInUp 0.6s ease;
        }

        .section-header {
            border-bottom: 3px solid #0d6efd;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .setting-item {
            padding: 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .setting-item:hover {
            background-color: rgba(13, 110, 253, 0.05);
            border-color: rgba(13, 110, 253, 0.2);
            transform: translateX(5px);
        }

        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
            box-shadow: 0 0 10px rgba(13, 110, 253, 0.5);
        }

        .form-control-lg, .form-select-lg {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control-lg:focus, .form-select-lg:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 15px rgba(13, 110, 253, 0.2);
        }

        .logo-placeholder {
            width: 120px;
            height: 120px;
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            transition: all 0.3s ease;
        }

        .logo-placeholder:hover {
            border-color: #0d6efd;
            background-color: rgba(13, 110, 253, 0.05);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
        }

        /* تحسينات النماذج المنبثقة */
        .modal-content {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
            overflow: hidden;
        }

        .modal-dialog {
            max-width: 600px;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            position: relative;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .modal-title {
            font-weight: 600;
            font-size: 1.1rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .modal-title i {
            margin-left: 8px;
            font-size: 1.2rem;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        .btn-close {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            opacity: 1;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .modal-body {
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
        }

        .modal-body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="grad" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.8);stop-opacity:1" /><stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:1" /></radialGradient></defs><circle cx="20" cy="20" r="15" fill="url(%23grad)"/><circle cx="80" cy="80" r="10" fill="url(%23grad)"/></svg>');
            opacity: 0.5;
            pointer-events: none;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.3rem;
            font-size: 0.85rem;
            position: relative;
        }

        .form-label i {
            margin-left: 6px;
            color: #667eea;
            font-size: 0.9rem;
        }

        .form-control, .form-select {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 0.5rem 0.75rem;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .mb-3 {
            margin-bottom: 1rem !important;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
            background: white;
            transform: translateY(-2px);
        }

        .form-control::placeholder {
            color: #adb5bd;
            font-style: italic;
        }

        .modal-footer {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            padding: 1rem 1.5rem;
            position: relative;
        }

        .modal-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 1.5rem;
            right: 1.5rem;
            height: 1px;
            background: linear-gradient(90deg, transparent, #dee2e6, transparent);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
        }

        /* تأثيرات خاصة للحقول */
        .form-floating {
            position: relative;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            opacity: 0.65;
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            color: #667eea;
        }

        /* تحسين النماذج المختلفة */
        .employee-form .modal-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .course-form .modal-header {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .leave-form .modal-header {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .delegation-form .modal-header {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .book-form .modal-header {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        }

        /* تأثيرات انيميشن للنماذج */
        .modal.fade .modal-dialog {
            transition: all 0.4s ease;
            transform: scale(0.8) translateY(-50px);
        }

        .modal.show .modal-dialog {
            transform: scale(1) translateY(0);
        }

        /* تحسين الصفوف والأعمدة */
        .row {
            position: relative;
        }

        .col-md-6, .col-md-12 {
            position: relative;
        }

        /* تأثيرات hover للحقول */
        .form-control:hover, .form-select:hover {
            border-color: #b8c5ea;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1);
        }

        /* تحسين textarea */
        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        textarea.form-control:focus {
            min-height: 120px;
        }

        /* تحسينات خاصة بتبويب المظهر */
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .color-option:hover {
            transform: scale(1.1);
            border-color: #fff;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .color-option.active {
            border-color: #fff;
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.5);
        }

        .color-option::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .color-option.active::after {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-people-fill me-2"></i>
                نظام إدارة الموظفين
            </a>
            <div class="navbar-nav ms-auto d-flex align-items-center">
                <span class="nav-link text-white me-3">
                    <i class="bi bi-clock-fill me-2"></i>
                    <span id="currentTime"></span>
                </span>
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-circle me-2"></i>
                        الحساب
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <a class="dropdown-item" href="#" onclick="showUserProfile()">
                                <i class="bi bi-person-fill me-2 text-primary"></i>
                                الملف الشخصي
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="showTab('settings')">
                                <i class="bi bi-gear-fill me-2 text-success"></i>
                                الإعدادات
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid p-4">


        <!-- رسالة الترحيب -->
        <div class="welcome-message">
            <h1>
                <i class="bi bi-house-heart-fill me-2"></i>
                نظام إدارة الموظفين
            </h1>
            <p class="mb-0">إدارة شاملة ومتطورة للموظفين والدورات والإجازات</p>
        </div>

        <!-- بطاقات الإحصائيات المحسنة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-people-fill"></i>
                        <h3>0</h3>
                        <p>إجمالي الموظفين</p>
                        <div class="mt-2">
                            <small class="opacity-75">
                                <i class="bi bi-plus-circle me-1"></i>
                                ابدأ بإضافة موظفين
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-book-fill"></i>
                        <h3>0</h3>
                        <p>الدورات التدريبية</p>
                        <div class="mt-2">
                            <small class="opacity-75">
                                <i class="bi bi-plus-circle me-1"></i>
                                لا توجد دورات
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-warning text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-check-fill"></i>
                        <h3>0</h3>
                        <p>الإجازات المعلقة</p>
                        <div class="mt-2">
                            <small class="opacity-75">
                                <i class="bi bi-plus-circle me-1"></i>
                                لا توجد إجازات
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-airplane-fill"></i>
                        <h3>0</h3>
                        <p>الإيفادات الجارية</p>
                        <div class="mt-2">
                            <small class="opacity-75">
                                <i class="bi bi-plus-circle me-1"></i>
                                لا توجد إيفادات
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التبويبات المضمون -->
        <div class="tabs-container">
            <ul class="nav nav-tabs nav-tabs-custom" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" href="#dashboard" data-tab="dashboard">
                        <i class="bi bi-house-fill text-success"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#employees" data-tab="employees">
                        <i class="bi bi-people-fill text-primary"></i>
                        إدارة الموظفين
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#service" data-tab="service">
                        <i class="bi bi-clock-history text-success"></i>
                        معرفة الخدمة
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#courses" data-tab="courses">
                        <i class="bi bi-book-fill text-info"></i>
                        الدورات التدريبية
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#leaves" data-tab="leaves">
                        <i class="bi bi-calendar-check-fill text-warning"></i>
                        الإجازات
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#delegations" data-tab="delegations">
                        <i class="bi bi-airplane-fill text-danger"></i>
                        الإيفادات
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#books" data-tab="books">
                        <i class="bi bi-book-fill text-purple"></i>
                        أرشفة الكتب
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#notifications" data-tab="notifications">
                        <i class="bi bi-bell-fill text-secondary"></i>
                        التنبيهات
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#reports" data-tab="reports">
                        <i class="bi bi-graph-up text-dark"></i>
                        التقارير
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#settings" data-tab="settings">
                        <i class="bi bi-gear-fill text-muted"></i>
                        الإعدادات
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link text-danger" href="#" onclick="logout()">
                        <i class="bi bi-box-arrow-right text-danger"></i>
                        خروج
                    </a>
                </li>
            </ul>

            <div class="tab-content" id="mainTabsContent">
                <!-- Dashboard Tab -->
                <div class="tab-pane active" id="dashboard">
                    <h4 class="fw-bold text-success">
                        <i class="bi bi-house-fill me-2"></i>
                        لوحة التحكم الرئيسية
                    </h4>
                    <p>مرحباً بك في لوحة التحكم الرئيسية. هنا يمكنك مراقبة جميع أنشطة النظام والحصول على نظرة عامة شاملة.</p>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <strong>تأكيد:</strong> شريط التبويبات يعمل بشكل مثالي! جرب النقر على التبويبات المختلفة أعلاه.
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">الإحصائيات السريعة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-people-fill text-primary me-2"></i>15 موظف نشط</li>
                                        <li><i class="bi bi-book-fill text-success me-2"></i>8 دورات تدريبية</li>
                                        <li><i class="bi bi-calendar-check-fill text-warning me-2"></i>3 إجازات معلقة</li>
                                        <li><i class="bi bi-airplane-fill text-info me-2"></i>2 إيفاد جاري</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">إجراءات سريعة</h6>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-outline-primary btn-sm me-2 mb-2" onclick="showAddEmployeeModal()">
                                        <i class="bi bi-person-plus-fill me-1"></i>موظف جديد
                                    </button>
                                    <button class="btn btn-outline-success btn-sm me-2 mb-2" onclick="showAddCourseModal()">
                                        <i class="bi bi-book-fill me-1"></i>دورة جديدة
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm me-2 mb-2" onclick="showAddLeaveModal()">
                                        <i class="bi bi-calendar-check-fill me-1"></i>إجازة جديدة
                                    </button>
                                    <button class="btn btn-outline-info btn-sm mb-2" onclick="showAddDelegationModal()">
                                        <i class="bi bi-airplane-fill me-1"></i>إيفاد جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employees Tab -->
                <div class="tab-pane" id="employees">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-primary">
                            <i class="bi bi-people-fill me-2"></i>
                            إدارة الموظفين
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="addNewEmployee()">
                                <i class="bi bi-person-plus-fill me-2"></i>
                                إضافة موظف جديد
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="exportEmployees()">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="printEmployees()">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <!-- عنوان الطباعة -->
                    <div class="print-header" style="display: none;">
                        <h2>قائمة الموظفين</h2>
                        <div class="print-date"></div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مكان العمل</th>
                                    <th>تاريخ البداية</th>
                                    <th>عنوان السكن</th>
                                    <th>تاريخ الميلاد</th>
                                    <th>الجوال</th>
                                    <th class="actions-column">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        <i class="bi bi-inbox fs-1 mb-2 d-block"></i>
                                        لا توجد بيانات موظفين حالياً
                                        <br>
                                        <small>اضغط على "إضافة موظف جديد" لبدء إدخال البيانات</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Service Tab -->
                <div class="tab-pane" id="service">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-success">
                            <i class="bi bi-clock-history me-2"></i>
                            معرفة الخدمة
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الخدمة')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة قائمة الخدمة')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مدة الخدمة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="bi bi-clock-history fs-1 mb-2 d-block"></i>
                                        لا توجد بيانات خدمة حالياً
                                        <br>
                                        <small>سيتم عرض مدة الخدمة عند إضافة موظفين</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Courses Tab -->
                <div class="tab-pane" id="courses">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-info">
                            <i class="bi bi-book-fill me-2"></i>
                            الدورات التدريبية
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="addNewCourse()">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة دورة جديدة
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="exportCourses()">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="printCourses()">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>اسم الدورة</th>
                                    <th>نوع الدورة</th>
                                    <th>المدة</th>
                                    <th>مكان الدورة</th>
                                    <th class="actions-column">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="9" class="text-center text-muted py-4">
                                        <i class="bi bi-book fs-1 mb-2 d-block"></i>
                                        لا توجد دورات تدريبية حالياً
                                        <br>
                                        <small>اضغط على "إضافة دورة جديدة" لبدء إدخال البيانات</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Leaves Tab -->
                <div class="tab-pane" id="leaves">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-warning">
                            <i class="bi bi-calendar-check-fill me-2"></i>
                            الإجازات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="addNewLeave()">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة إجازة جديدة
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="exportLeaves()">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="printLeaves()">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مكان العمل</th>
                                    <th>تاريخ الإجازة</th>
                                    <th>مدة الإجازة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="bi bi-calendar-x fs-1 mb-2 d-block"></i>
                                        لا توجد إجازات حالياً
                                        <br>
                                        <small>اضغط على "إضافة إجازة جديدة" لبدء إدخال البيانات</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Delegations Tab -->
                <div class="tab-pane" id="delegations">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-danger">
                            <i class="bi bi-airplane-fill me-2"></i>
                            الإيفادات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="addNewDelegation()">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة إيفاد جديد
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="exportDelegations()">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="printDelegations()">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>الوجهة</th>
                                    <th>الغرض</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="bi bi-airplane fs-1 mb-2 d-block"></i>
                                        لا توجد إيفادات حالياً
                                        <br>
                                        <small>اضغط على "إضافة إيفاد جديد" لبدء إدخال البيانات</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Books Tab -->
                <div class="tab-pane" id="books">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-purple">
                            <i class="bi bi-book-fill me-2"></i>
                            أرشفة الكتب
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary fw-bold" onclick="addNewBook()">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة كتاب جديد
                            </button>
                            <button type="button" class="btn btn-success fw-bold" onclick="exportBooks()">
                                <i class="bi bi-file-earmark-excel-fill me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="printBooks()">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-archive me-2"></i>
                                        إحصائيات الأرشيف
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-12 mb-3">
                                            <h4 class="text-muted">0</h4>
                                            <small>إجمالي الكتب المؤرشفة</small>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="text-info">0</h5>
                                            <small>هذا الشهر</small>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="text-warning">0</h5>
                                            <small>هذا الأسبوع</small>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <button class="btn btn-outline-success btn-sm" onclick="showArchiveStats()">
                                            <i class="bi bi-bar-chart me-1"></i>
                                            تفاصيل الإحصائيات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>التسلسل</th>
                                            <th>رقم الكتاب</th>
                                            <th>تاريخ الكتاب</th>
                                            <th>الموضوع</th>
                                            <th>الجهة الوارد منها</th>
                                            <th>الكتاب</th>
                                            <th>الأضبارة</th>
                                            <th>الملاحظات</th>
                                            <th>المرفقات</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="10" class="text-center text-muted py-4">
                                                <i class="bi bi-archive fs-1 mb-2 d-block"></i>
                                                لا توجد كتب مؤرشفة حالياً
                                                <br>
                                                <small>اضغط على "إضافة كتاب جديد" لبدء الأرشفة</small>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Tab -->
                <div class="tab-pane" id="notifications">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-secondary">
                            <i class="bi bi-bell-fill me-2"></i>
                            التنبيهات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة تنبيه جديد')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                تنبيه جديد
                            </button>
                            <button type="button" class="btn btn-warning fw-bold" onclick="showAlert('تحديد الكل كمقروء')">
                                <i class="bi bi-check-all me-2"></i>
                                تحديد كمقروء
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-bell-slash fs-1 mb-3 d-block"></i>
                                <h5>لا توجد تنبيهات حالياً</h5>
                                <p>سيتم عرض التنبيهات والإشعارات هنا عند توفرها</p>
                                <button class="btn btn-outline-primary" onclick="showAlert('تم تحديث التنبيهات', 'info')">
                                    <i class="bi bi-arrow-clockwise me-2"></i>
                                    تحديث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">ملخص التنبيهات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-12 mb-2">
                                            <h4 class="text-muted">0</h4>
                                            <small>غير مقروءة</small>
                                        </div>
                                        <div class="col-12">
                                            <h4 class="text-muted">0</h4>
                                            <small>إجمالي اليوم</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane" id="reports">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-dark">
                            <i class="bi bi-graph-up me-2"></i>
                            التقارير
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير التقرير')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة التقرير')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">تقرير الموظفين</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="employeesChart" width="400" height="200"></canvas>
                                    <div class="mt-3">
                                        <small class="text-muted">إجمالي الموظفين: 0</small><br>
                                        <small class="text-muted">موظفين جدد هذا الشهر: 0</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">تقرير الدورات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h4 class="text-muted">0</h4>
                                            <small>دورات مكتملة</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-muted">0</h4>
                                            <small>قيد التنفيذ</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-muted">0</h4>
                                            <small>مخططة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane" id="settings">
                    <!-- Header Section -->
                    <div class="settings-header mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="settings-icon me-3">
                                        <i class="bi bi-gear-fill fs-1 text-primary"></i>
                                    </div>
                                    <div>
                                        <h3 class="fw-bold text-primary mb-1">إعدادات النظام</h3>
                                        <p class="text-muted mb-0">تخصيص وإدارة إعدادات نظام إدارة الموظفين</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group-vertical d-grid gap-2">
                                    <button type="button" class="btn btn-success fw-bold shadow-sm" onclick="saveSettings()">
                                        <i class="bi bi-check-circle-fill me-2"></i>
                                        حفظ الإعدادات
                                    </button>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-outline-warning fw-bold" onclick="resetSettings()">
                                            <i class="bi bi-arrow-clockwise me-2"></i>
                                            استعادة افتراضي
                                        </button>
                                        <button type="button" class="btn btn-outline-info fw-bold" onclick="exportSettings()">
                                            <i class="bi bi-download me-2"></i>
                                            تصدير
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Navigation -->
                    <div class="settings-nav mb-4">
                        <ul class="nav nav-pills nav-fill" id="settingsNav" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active fw-bold" id="company-tab" data-bs-toggle="pill" data-bs-target="#company-settings" type="button" role="tab">
                                    <i class="bi bi-building me-2"></i>
                                    معلومات الشركة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-bold" id="system-tab" data-bs-toggle="pill" data-bs-target="#system-settings" type="button" role="tab">
                                    <i class="bi bi-sliders me-2"></i>
                                    إعدادات النظام
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-bold" id="appearance-tab" data-bs-toggle="pill" data-bs-target="#appearance-settings" type="button" role="tab">
                                    <i class="bi bi-palette me-2"></i>
                                    المظهر
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-bold" id="data-tab" data-bs-toggle="pill" data-bs-target="#data-settings" type="button" role="tab">
                                    <i class="bi bi-database me-2"></i>
                                    البيانات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-bold" id="about-tab" data-bs-toggle="pill" data-bs-target="#about-settings" type="button" role="tab">
                                    <i class="bi bi-info-circle me-2"></i>
                                    حول النظام
                                </button>
                            </li>
                        </ul>
                    </div>

                    <!-- Settings Content -->
                    <div class="tab-content" id="settingsContent">

                        <!-- Company Settings -->
                        <div class="tab-pane fade show active" id="company-settings" role="tabpanel">
                            <div class="settings-section">
                                <div class="section-header mb-4">
                                    <h5 class="fw-bold text-primary mb-2">
                                        <i class="bi bi-building-fill me-2"></i>
                                        معلومات الشركة
                                    </h5>
                                    <p class="text-muted">إدارة البيانات الأساسية للشركة أو المؤسسة</p>
                                </div>

                                <div class="row">
                                    <div class="col-lg-8">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body p-4">
                                                <div class="row">
                                                    <div class="col-md-6 mb-4">
                                                        <label class="form-label fw-bold text-dark">
                                                            <i class="bi bi-building me-2 text-primary"></i>
                                                            اسم الشركة
                                                        </label>
                                                        <input type="text" class="form-control form-control-lg" id="companyName" placeholder="أدخل اسم الشركة">
                                                    </div>
                                                    <div class="col-md-6 mb-4">
                                                        <label class="form-label fw-bold text-dark">
                                                            <i class="bi bi-envelope me-2 text-primary"></i>
                                                            البريد الإلكتروني
                                                        </label>
                                                        <input type="email" class="form-control form-control-lg" id="companyEmail" placeholder="<EMAIL>">
                                                    </div>
                                                    <div class="col-md-6 mb-4">
                                                        <label class="form-label fw-bold text-dark">
                                                            <i class="bi bi-telephone me-2 text-primary"></i>
                                                            رقم الهاتف
                                                        </label>
                                                        <input type="tel" class="form-control form-control-lg" id="companyPhone" placeholder="0112345678">
                                                    </div>
                                                    <div class="col-md-6 mb-4">
                                                        <label class="form-label fw-bold text-dark">
                                                            <i class="bi bi-globe me-2 text-primary"></i>
                                                            الموقع الإلكتروني
                                                        </label>
                                                        <input type="url" class="form-control form-control-lg" id="companyWebsite" placeholder="https://www.company.com">
                                                    </div>
                                                    <div class="col-12 mb-4">
                                                        <label class="form-label fw-bold text-dark">
                                                            <i class="bi bi-geo-alt me-2 text-primary"></i>
                                                            العنوان
                                                        </label>
                                                        <textarea class="form-control" id="companyAddress" rows="3" placeholder="العنوان الكامل للشركة"></textarea>
                                                    </div>
                                                    <div class="col-md-6 mb-4">
                                                        <label class="form-label fw-bold text-dark">
                                                            <i class="bi bi-person-badge me-2 text-primary"></i>
                                                            اسم المدير العام
                                                        </label>
                                                        <input type="text" class="form-control form-control-lg" id="companyCEO" placeholder="اسم المدير العام">
                                                    </div>
                                                    <div class="col-md-6 mb-4">
                                                        <label class="form-label fw-bold text-dark">
                                                            <i class="bi bi-calendar-event me-2 text-primary"></i>
                                                            تاريخ التأسيس
                                                        </label>
                                                        <input type="date" class="form-control form-control-lg" id="companyEstablished">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-body p-4 text-center">
                                                <div class="company-logo-section">
                                                    <div class="logo-placeholder mb-3">
                                                        <i class="bi bi-image fs-1 text-muted"></i>
                                                    </div>
                                                    <h6 class="fw-bold mb-3">شعار الشركة</h6>
                                                    <button class="btn btn-outline-primary mb-2 w-100" onclick="uploadLogo()">
                                                        <i class="bi bi-upload me-2"></i>
                                                        رفع الشعار
                                                    </button>
                                                    <button class="btn btn-outline-danger w-100" onclick="removeLogo()">
                                                        <i class="bi bi-trash me-2"></i>
                                                        حذف الشعار
                                                    </button>
                                                    <small class="text-muted d-block mt-2">
                                                        الحد الأقصى: 2MB<br>
                                                        الصيغ المدعومة: PNG, JPG
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings -->
                        <div class="tab-pane fade" id="system-settings" role="tabpanel">
                            <div class="settings-section">
                                <div class="section-header mb-4">
                                    <h5 class="fw-bold text-success mb-2">
                                        <i class="bi bi-sliders me-2"></i>
                                        إعدادات النظام
                                    </h5>
                                    <p class="text-muted">تخصيص سلوك النظام والتفضيلات العامة</p>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="card border-0 shadow-sm mb-4">
                                            <div class="card-header bg-light">
                                                <h6 class="fw-bold mb-0">
                                                    <i class="bi bi-toggles me-2 text-success"></i>
                                                    التفضيلات العامة
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="setting-item mb-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="fw-bold mb-1">
                                                                <i class="bi bi-moon-fill me-2 text-primary"></i>
                                                                الوضع الليلي
                                                            </h6>
                                                            <small class="text-muted">تفعيل المظهر الداكن للنظام</small>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="darkMode">
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="setting-item mb-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="fw-bold mb-1">
                                                                <i class="bi bi-bell-fill me-2 text-warning"></i>
                                                                التنبيهات
                                                            </h6>
                                                            <small class="text-muted">إظهار الإشعارات والتنبيهات</small>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="notifications" checked>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="setting-item mb-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="fw-bold mb-1">
                                                                <i class="bi bi-cloud-check-fill me-2 text-info"></i>
                                                                الحفظ التلقائي
                                                            </h6>
                                                            <small class="text-muted">حفظ البيانات تلقائياً</small>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="autoSave" checked>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="setting-item mb-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="fw-bold mb-1">
                                                                <i class="bi bi-volume-up-fill me-2 text-success"></i>
                                                                الأصوات
                                                            </h6>
                                                            <small class="text-muted">تشغيل الأصوات التفاعلية</small>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="soundEffects" checked>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="card border-0 shadow-sm mb-4">
                                            <div class="card-header bg-light">
                                                <h6 class="fw-bold mb-0">
                                                    <i class="bi bi-gear me-2 text-success"></i>
                                                    إعدادات متقدمة
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-translate me-2 text-primary"></i>
                                                        اللغة
                                                    </label>
                                                    <select class="form-select form-select-lg" id="language">
                                                        <option value="ar" selected>العربية</option>
                                                        <option value="en">English</option>
                                                    </select>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-clock me-2 text-primary"></i>
                                                        المنطقة الزمنية
                                                    </label>
                                                    <select class="form-select form-select-lg" id="timezone">
                                                        <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                                        <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                                    </select>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-calendar me-2 text-primary"></i>
                                                        نوع التقويم
                                                    </label>
                                                    <select class="form-select form-select-lg" id="calendar">
                                                        <option value="gregorian" selected>ميلادي</option>
                                                        <option value="hijri">هجري</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Appearance Settings -->
                        <div class="tab-pane fade" id="appearance-settings" role="tabpanel">
                            <div class="settings-section">
                                <div class="section-header mb-4">
                                    <h5 class="fw-bold text-info mb-2">
                                        <i class="bi bi-palette me-2"></i>
                                        إعدادات المظهر
                                    </h5>
                                    <p class="text-muted">تخصيص ألوان وخطوط واجهة النظام</p>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="card border-0 shadow-sm mb-4">
                                            <div class="card-header bg-light">
                                                <h6 class="fw-bold mb-0">
                                                    <i class="bi bi-droplet me-2 text-info"></i>
                                                    الألوان والثيمات
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-palette me-2 text-primary"></i>
                                                        اللون الأساسي
                                                    </label>
                                                    <div class="color-options d-flex gap-2 flex-wrap">
                                                        <div class="color-option" style="background: #0d6efd;" data-color="#0d6efd" title="أزرق"></div>
                                                        <div class="color-option" style="background: #198754;" data-color="#198754" title="أخضر"></div>
                                                        <div class="color-option" style="background: #dc3545;" data-color="#dc3545" title="أحمر"></div>
                                                        <div class="color-option" style="background: #fd7e14;" data-color="#fd7e14" title="برتقالي"></div>
                                                        <div class="color-option" style="background: #6f42c1;" data-color="#6f42c1" title="بنفسجي"></div>
                                                        <div class="color-option" style="background: #20c997;" data-color="#20c997" title="تركوازي"></div>
                                                    </div>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-brightness-high me-2 text-warning"></i>
                                                        سطوع الواجهة
                                                    </label>
                                                    <input type="range" class="form-range" id="brightness" min="50" max="100" value="100">
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-contrast me-2 text-dark"></i>
                                                        التباين
                                                    </label>
                                                    <input type="range" class="form-range" id="contrast" min="80" max="120" value="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="card border-0 shadow-sm mb-4">
                                            <div class="card-header bg-light">
                                                <h6 class="fw-bold mb-0">
                                                    <i class="bi bi-fonts me-2 text-info"></i>
                                                    الخطوط والنصوص
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-type me-2 text-primary"></i>
                                                        نوع الخط
                                                    </label>
                                                    <select class="form-select" id="fontFamily">
                                                        <option value="Tajawal" selected>Tajawal</option>
                                                        <option value="Cairo">Cairo</option>
                                                        <option value="Amiri">Amiri</option>
                                                        <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                                                    </select>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-type-h1 me-2 text-primary"></i>
                                                        حجم الخط
                                                    </label>
                                                    <select class="form-select" id="fontSize">
                                                        <option value="small">صغير</option>
                                                        <option value="medium" selected>متوسط</option>
                                                        <option value="large">كبير</option>
                                                        <option value="extra-large">كبير جداً</option>
                                                    </select>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-type-bold me-2 text-primary"></i>
                                                        سماكة الخط
                                                    </label>
                                                    <select class="form-select" id="fontWeight">
                                                        <option value="normal">عادي</option>
                                                        <option value="medium">متوسط</option>
                                                        <option value="bold" selected>عريض</option>
                                                        <option value="extra-bold">عريض جداً</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Settings -->
                        <div class="tab-pane fade" id="data-settings" role="tabpanel">
                            <div class="settings-section">
                                <div class="section-header mb-4">
                                    <h5 class="fw-bold text-success mb-2">
                                        <i class="bi bi-sliders me-2"></i>
                                        إعدادات النظام
                                    </h5>
                                    <p class="text-muted">تخصيص سلوك النظام والتفضيلات العامة</p>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="card border-0 shadow-sm mb-4">
                                            <div class="card-header bg-light">
                                                <h6 class="fw-bold mb-0">
                                                    <i class="bi bi-toggles me-2 text-success"></i>
                                                    التفضيلات العامة
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="setting-item mb-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="fw-bold mb-1">
                                                                <i class="bi bi-moon-fill me-2 text-primary"></i>
                                                                الوضع الليلي
                                                            </h6>
                                                            <small class="text-muted">تفعيل المظهر الداكن للنظام</small>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="darkMode">
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="setting-item mb-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="fw-bold mb-1">
                                                                <i class="bi bi-bell-fill me-2 text-warning"></i>
                                                                التنبيهات
                                                            </h6>
                                                            <small class="text-muted">إظهار الإشعارات والتنبيهات</small>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="notifications" checked>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="setting-item mb-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="fw-bold mb-1">
                                                                <i class="bi bi-cloud-check-fill me-2 text-info"></i>
                                                                الحفظ التلقائي
                                                            </h6>
                                                            <small class="text-muted">حفظ البيانات تلقائياً</small>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="autoSave" checked>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="setting-item mb-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="fw-bold mb-1">
                                                                <i class="bi bi-volume-up-fill me-2 text-success"></i>
                                                                الأصوات
                                                            </h6>
                                                            <small class="text-muted">تشغيل الأصوات التفاعلية</small>
                                                        </div>
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" id="soundEffects" checked>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="card border-0 shadow-sm mb-4">
                                            <div class="card-header bg-light">
                                                <h6 class="fw-bold mb-0">
                                                    <i class="bi bi-gear me-2 text-success"></i>
                                                    إعدادات متقدمة
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-translate me-2 text-primary"></i>
                                                        اللغة
                                                    </label>
                                                    <select class="form-select form-select-lg" id="language">
                                                        <option value="ar" selected>العربية</option>
                                                        <option value="en">English</option>
                                                    </select>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-clock me-2 text-primary"></i>
                                                        المنطقة الزمنية
                                                    </label>
                                                    <select class="form-select form-select-lg" id="timezone">
                                                        <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                                        <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                                    </select>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label fw-bold">
                                                        <i class="bi bi-calendar me-2 text-primary"></i>
                                                        نوع التقويم
                                                    </label>
                                                    <select class="form-select form-select-lg" id="calendar">
                                                        <option value="gregorian" selected>ميلادي</option>
                                                        <option value="hijri">هجري</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <!-- إعدادات الطباعة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-printer-fill me-2"></i>
                                        إعدادات الطباعة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="printHeader" checked>
                                        <label class="form-check-label fw-bold" for="printHeader">
                                            طباعة رأس الصفحة
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="printDate" checked>
                                        <label class="form-check-label fw-bold" for="printDate">
                                            طباعة التاريخ
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="printLogo">
                                        <label class="form-check-label fw-bold" for="printLogo">
                                            طباعة شعار الشركة
                                        </label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">حجم الخط للطباعة</label>
                                        <select class="form-select" id="printFontSize">
                                            <option value="10px">صغير (10px)</option>
                                            <option value="12px" selected>متوسط (12px)</option>
                                            <option value="14px">كبير (14px)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="bi bi-database-fill me-2"></i>
                                        إدارة البيانات
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="backupData()">
                                            <i class="bi bi-cloud-upload me-2"></i>
                                            نسخ احتياطي للبيانات
                                        </button>
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="restoreData()">
                                            <i class="bi bi-cloud-download me-2"></i>
                                            استعادة البيانات
                                        </button>
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="exportAllData()">
                                            <i class="bi bi-file-earmark-excel me-2"></i>
                                            تصدير جميع البيانات
                                        </button>
                                        <button class="btn btn-outline-danger w-100" onclick="clearAllData()">
                                            <i class="bi bi-trash3 me-2"></i>
                                            مسح جميع البيانات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات المظهر -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-palette-fill me-2"></i>
                                        إعدادات المظهر والألوان
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-bold">اللون الأساسي</label>
                                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#0d6efd">
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-bold">لون النجاح</label>
                                            <input type="color" class="form-control form-control-color" id="successColor" value="#198754">
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-bold">لون التحذير</label>
                                            <input type="color" class="form-control form-control-color" id="warningColor" value="#ffc107">
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label fw-bold">لون الخطر</label>
                                            <input type="color" class="form-control form-control-color" id="dangerColor" value="#dc3545">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label fw-bold">نوع الخط</label>
                                            <select class="form-select" id="fontFamily">
                                                <option value="Tajawal" selected>Tajawal</option>
                                                <option value="Cairo">Cairo</option>
                                                <option value="Amiri">Amiri</option>
                                                <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label fw-bold">حجم الخط الأساسي</label>
                                            <select class="form-select" id="baseFontSize">
                                                <option value="14px">صغير (14px)</option>
                                                <option value="16px" selected>متوسط (16px)</option>
                                                <option value="18px">كبير (18px)</option>
                                                <option value="20px">كبير جداً (20px)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات النظام -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card border-dark">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        معلومات النظام
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>إصدار النظام:</strong><br>
                                            <span class="text-muted">v1.0.0</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>تاريخ آخر تحديث:</strong><br>
                                            <span class="text-muted" id="lastUpdate">-</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>عدد الموظفين:</strong><br>
                                            <span class="text-muted" id="totalEmployees">0</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>حجم البيانات:</strong><br>
                                            <span class="text-muted" id="dataSize">0 KB</span>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <button class="btn btn-outline-primary me-2" onclick="showUserGuide()">
                                            <i class="bi bi-book me-2"></i>
                                            دليل المستخدم
                                        </button>
                                        <button class="btn btn-outline-info me-2" onclick="showAbout()">
                                            <i class="bi bi-info-circle me-2"></i>
                                            حول النظام
                                        </button>
                                        <button class="btn btn-outline-success" onclick="checkForUpdates()">
                                            <i class="bi bi-arrow-repeat me-2"></i>
                                            فحص التحديثات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                <i class="bi bi-building me-2"></i>
                نظام إدارة الموظفين © 2024 - جميع الحقوق محفوظة
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم تحميل النظام بنجاح');

            // تحديث الوقت
            updateTime();
            setInterval(updateTime, 1000);

            // تحميل الإعدادات المحفوظة
            loadSavedSettings();

            // تحديث الجداول بالبيانات المحفوظة
            updateEmployeesTable();
            updateCoursesTable();
            updateLeavesTable();
            updateDelegationsTable();
            updateBooksTable();

            // تحريك البطاقات عند التحميل
            animateCards();

            // تفعيل التبويبات
            const tabLinks = document.querySelectorAll('.nav-tabs-custom .nav-link');
            const tabPanes = document.querySelectorAll('.tab-pane');

            console.log(`✅ تم العثور على ${tabLinks.length} تبويب`);

            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // إزالة الفئة النشطة من جميع التبويبات
                    tabLinks.forEach(l => l.classList.remove('active'));
                    tabPanes.forEach(p => p.classList.remove('active'));

                    // تفعيل التبويب المحدد
                    this.classList.add('active');
                    const targetId = this.getAttribute('data-tab');
                    const targetPane = document.getElementById(targetId);

                    if (targetPane) {
                        targetPane.classList.add('active');
                        console.log(`✅ تم تفعيل تبويب: ${targetId}`);

                        // إظهار تنبيه
                        showAlert(`تم فتح تبويب: ${this.textContent.trim()}`);
                    }
                });
            });

            console.log('✅ شريط التبويبات يعمل بشكل مثالي');
        });

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // تحريك البطاقات عند التحميل
        function animateCards() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                // إخفاء البطاقة في البداية
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px) scale(0.8)';
                card.style.transition = 'all 0.6s ease';

                // إظهار البطاقة بتأخير متدرج
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, index * 200);
            });
        }

        // وظائف إدارة الموظفين
        function showAddEmployeeModal() {
            showModal('إضافة موظف جديد', `
                <form id="addEmployeeForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الشركة</label>
                            <input type="text" class="form-control" id="companyNumber" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="fullName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المسمى الوظيفي</label>
                            <input type="text" class="form-control" id="jobTitle" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مكان العمل</label>
                            <select class="form-select" id="workLocation" required>
                                <option value="">اختر المدينة</option>
                                <option value="الرياض">الرياض</option>
                                <option value="جدة">جدة</option>
                                <option value="الدمام">الدمام</option>
                                <option value="مكة">مكة</option>
                                <option value="المدينة">المدينة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="startDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="birthDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الجوال</label>
                            <input type="tel" class="form-control" id="mobile" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">عنوان السكن</label>
                            <input type="text" class="form-control" id="address" required>
                        </div>
                    </div>
                </form>
            `, 'addEmployee');
        }

        function showAddCourseModal() {
            showModal('إضافة دورة تدريبية جديدة', `
                <form id="addCourseForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اختر الموظف</label>
                            <select class="form-select" id="courseEmployee" required>
                                <option value="">اختر الموظف</option>
                                <option value="EMP001">أحمد محمد علي</option>
                                <option value="EMP002">فاطمة خالد السعد</option>
                                <option value="EMP003">محمد عبدالله القحطاني</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الدورة</label>
                            <input type="text" class="form-control" id="courseName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الدورة</label>
                            <select class="form-select" id="courseType" required>
                                <option value="">اختر النوع</option>
                                <option value="تقنية">تقنية</option>
                                <option value="مالية">مالية</option>
                                <option value="إدارية">إدارية</option>
                                <option value="تطويرية">تطويرية</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مدة الدورة</label>
                            <input type="text" class="form-control" id="courseDuration" placeholder="مثال: 5 أيام" required>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label class="form-label">مكان الدورة</label>
                            <input type="text" class="form-control" id="courseLocation" required>
                        </div>
                    </div>
                </form>
            `, 'addCourse');
        }

        function showAddLeaveModal() {
            showModal('إضافة إجازة جديدة', `
                <form id="addLeaveForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اختر الموظف</label>
                            <select class="form-select" id="leaveEmployee" required>
                                <option value="">اختر الموظف</option>
                                <option value="EMP001">أحمد محمد علي</option>
                                <option value="EMP002">فاطمة خالد السعد</option>
                                <option value="EMP003">محمد عبدالله القحطاني</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الإجازة</label>
                            <select class="form-select" id="leaveType" required>
                                <option value="">اختر النوع</option>
                                <option value="سنوية">إجازة سنوية</option>
                                <option value="مرضية">إجازة مرضية</option>
                                <option value="طارئة">إجازة طارئة</option>
                                <option value="أمومة">إجازة أمومة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ بداية الإجازة</label>
                            <input type="date" class="form-control" id="leaveStartDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ نهاية الإجازة</label>
                            <input type="date" class="form-control" id="leaveEndDate" required>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label class="form-label">سبب الإجازة</label>
                            <textarea class="form-control" id="leaveReason" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            `, 'addLeave');
        }

        function showAddDelegationModal() {
            showModal('إضافة إيفاد جديد', `
                <form id="addDelegationForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اختر الموظف</label>
                            <select class="form-select" id="delegationEmployee" required>
                                <option value="">اختر الموظف</option>
                                <option value="EMP001">أحمد محمد علي</option>
                                <option value="EMP002">فاطمة خالد السعد</option>
                                <option value="EMP003">محمد عبدالله القحطاني</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الوجهة</label>
                            <input type="text" class="form-control" id="destination" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="delegationStartDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" id="delegationEndDate" required>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label class="form-label">الغرض من الإيفاد</label>
                            <textarea class="form-control" id="delegationPurpose" rows="3" required></textarea>
                        </div>
                    </div>
                </form>
            `, 'addDelegation');
        }

        // وظائف التصدير والطباعة
        function exportEmployees() {
            showAlert('جاري تصدير بيانات الموظفين...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الموظفين بنجاح!', 'success');
            }, 2000);
        }

        function printEmployees() {
            showAlert('جاري تحضير الطباعة...', 'info');

            // إضافة تاريخ الطباعة
            const printDate = document.querySelector('.print-date');
            if (printDate) {
                const now = new Date();
                const dateString = now.toLocaleDateString('ar-SA', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                const timeString = now.toLocaleTimeString('ar-SA');
                printDate.textContent = `تاريخ الطباعة: ${dateString} - ${timeString}`;
            }

            setTimeout(() => {
                window.print();
            }, 1000);
        }

        function exportCourses() {
            showAlert('جاري تصدير بيانات الدورات...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الدورات بنجاح!', 'success');
            }, 2000);
        }

        function printCourses() {
            showAlert('جاري تحضير طباعة الدورات...', 'info');
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        // وظائف التعديل والحذف
        function editEmployee(empId, empName) {
            showAlert(`جاري تحميل بيانات الموظف: ${empName}`, 'info');
            setTimeout(() => {
                showAddEmployeeModal();
                showAlert(`تم تحميل بيانات ${empName} للتعديل`, 'success');
            }, 1000);
        }

        function deleteEmployee(empId, empName) {
            if (confirm(`هل أنت متأكد من حذف الموظف: ${empName}؟`)) {
                showAlert(`جاري حذف الموظف: ${empName}`, 'info');
                setTimeout(() => {
                    showAlert(`تم حذف الموظف ${empName} بنجاح`, 'success');
                }, 1500);
            }
        }

        function editCourse(empId, courseName) {
            showAlert(`جاري تحميل بيانات الدورة: ${courseName}`, 'info');
            setTimeout(() => {
                showAddCourseModal();
                showAlert(`تم تحميل بيانات الدورة للتعديل`, 'success');
            }, 1000);
        }

        function deleteCourse(empId, courseName) {
            if (confirm(`هل أنت متأكد من حذف الدورة: ${courseName}؟`)) {
                showAlert(`جاري حذف الدورة: ${courseName}`, 'info');
                setTimeout(() => {
                    showAlert(`تم حذف الدورة بنجاح`, 'success');
                }, 1500);
            }
        }

        function editLeave(empId, empName) {
            showAlert(`جاري تحميل بيانات إجازة: ${empName}`, 'info');
            setTimeout(() => {
                showAddLeaveModal();
                showAlert(`تم تحميل بيانات الإجازة للتعديل`, 'success');
            }, 1000);
        }

        function deleteLeave(empId, empName) {
            if (confirm(`هل أنت متأكد من حذف إجازة: ${empName}؟`)) {
                showAlert(`جاري حذف الإجازة`, 'info');
                setTimeout(() => {
                    showAlert(`تم حذف الإجازة بنجاح`, 'success');
                }, 1500);
            }
        }

        // وظائف أزرار لوحة التحكم
        function refreshDashboard() {
            showAlert('جاري تحديث لوحة التحكم...', 'info');
            setTimeout(() => {
                showAlert('تم تحديث لوحة التحكم بنجاح!', 'success');
                // تحديث الأرقام في البطاقات
                updateStatistics();
            }, 2000);
        }

        function showQuickActions() {
            showAlert('عرض الإجراءات السريعة', 'info');
        }

        function updateStatistics() {
            // محاكاة تحديث الإحصائيات
            const stats = [
                { id: 'employeesCount', value: Math.floor(Math.random() * 20) + 10 },
                { id: 'coursesCount', value: Math.floor(Math.random() * 15) + 5 },
                { id: 'leavesCount', value: Math.floor(Math.random() * 10) + 1 },
                { id: 'delegationsCount', value: Math.floor(Math.random() * 5) + 1 }
            ];

            stats.forEach(stat => {
                const element = document.querySelector(`#${stat.id}`);
                if (element) {
                    element.textContent = stat.value;
                }
            });
        }

        // وظائف الإعدادات
        function saveSettings() {
            const settings = {
                companyName: document.getElementById('companyName').value,
                companyEmail: document.getElementById('companyEmail').value,
                companyPhone: document.getElementById('companyPhone').value,
                companyAddress: document.getElementById('companyAddress').value,
                darkMode: document.getElementById('darkMode').checked,
                notifications: document.getElementById('notifications').checked,
                autoSave: document.getElementById('autoSave').checked,
                soundEffects: document.getElementById('soundEffects').checked,
                language: document.getElementById('language').value,
                printHeader: document.getElementById('printHeader').checked,
                printDate: document.getElementById('printDate').checked,
                printLogo: document.getElementById('printLogo').checked,
                printFontSize: document.getElementById('printFontSize').value,
                primaryColor: document.getElementById('primaryColor').value,
                successColor: document.getElementById('successColor').value,
                warningColor: document.getElementById('warningColor').value,
                dangerColor: document.getElementById('dangerColor').value,
                fontFamily: document.getElementById('fontFamily').value,
                baseFontSize: document.getElementById('baseFontSize').value
            };

            localStorage.setItem('employeeSystemSettings', JSON.stringify(settings));
            showAlert('تم حفظ الإعدادات بنجاح!', 'success');
            applySettings(settings);
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع الإعدادات الحالية.')) {
                localStorage.removeItem('employeeSystemSettings');
                loadDefaultSettings();
                showAlert('تم استعادة الإعدادات الافتراضية', 'info');
            }
        }

        function exportSettings() {
            const settings = localStorage.getItem('employeeSystemSettings');
            if (settings) {
                const blob = new Blob([settings], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'employee_system_settings.json';
                a.click();
                URL.revokeObjectURL(url);
                showAlert('تم تصدير الإعدادات بنجاح!', 'success');
            } else {
                showAlert('لا توجد إعدادات للتصدير', 'warning');
            }
        }

        function loadDefaultSettings() {
            document.getElementById('companyName').value = '';
            document.getElementById('companyEmail').value = '';
            document.getElementById('companyPhone').value = '';
            document.getElementById('companyAddress').value = '';
            document.getElementById('darkMode').checked = false;
            document.getElementById('notifications').checked = true;
            document.getElementById('autoSave').checked = true;
            document.getElementById('soundEffects').checked = true;
            document.getElementById('language').value = 'ar';
            document.getElementById('printHeader').checked = true;
            document.getElementById('printDate').checked = true;
            document.getElementById('printLogo').checked = false;
            document.getElementById('printFontSize').value = '12px';
            document.getElementById('primaryColor').value = '#0d6efd';
            document.getElementById('successColor').value = '#198754';
            document.getElementById('warningColor').value = '#ffc107';
            document.getElementById('dangerColor').value = '#dc3545';
            document.getElementById('fontFamily').value = 'Tajawal';
            document.getElementById('baseFontSize').value = '16px';
        }

        function applySettings(settings) {
            // تطبيق الألوان
            document.documentElement.style.setProperty('--bs-primary', settings.primaryColor);
            document.documentElement.style.setProperty('--bs-success', settings.successColor);
            document.documentElement.style.setProperty('--bs-warning', settings.warningColor);
            document.documentElement.style.setProperty('--bs-danger', settings.dangerColor);

            // تطبيق الخط
            document.body.style.fontFamily = settings.fontFamily;
            document.body.style.fontSize = settings.baseFontSize;

            // تطبيق الوضع الليلي
            if (settings.darkMode) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }
        }

        // وظائف إدارة البيانات
        function backupData() {
            const allData = {
                settings: localStorage.getItem('employeeSystemSettings'),
                employees: localStorage.getItem('employees') || '[]',
                courses: localStorage.getItem('courses') || '[]',
                leaves: localStorage.getItem('leaves') || '[]',
                delegations: localStorage.getItem('delegations') || '[]',
                books: localStorage.getItem('books') || '[]',
                notifications: localStorage.getItem('notifications') || '[]'
            };

            const blob = new Blob([JSON.stringify(allData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `employee_system_backup_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            showAlert('تم إنشاء نسخة احتياطية بنجاح!', 'success');
        }

        function restoreData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            Object.keys(data).forEach(key => {
                                if (data[key]) {
                                    localStorage.setItem(key, data[key]);
                                }
                            });
                            showAlert('تم استعادة البيانات بنجاح! سيتم إعادة تحميل الصفحة.', 'success');
                            setTimeout(() => location.reload(), 2000);
                        } catch (error) {
                            showAlert('خطأ في قراءة ملف النسخة الاحتياطية', 'danger');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function exportAllData() {
            showAlert('جاري تصدير جميع البيانات...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير جميع البيانات بنجاح!', 'success');
            }, 2000);
        }

        function clearAllData() {
            if (confirm('تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً!\n\nهل أنت متأكد من المتابعة؟')) {
                if (confirm('تأكيد أخير: سيتم حذف جميع الموظفين والدورات والإجازات والإعدادات!\n\nهل تريد المتابعة؟')) {
                    localStorage.clear();
                    showAlert('تم مسح جميع البيانات. سيتم إعادة تحميل الصفحة.', 'info');
                    setTimeout(() => location.reload(), 2000);
                }
            }
        }

        // وظائف إضافية
        function showUserGuide() {
            showModal('دليل المستخدم', `
                <div class="text-center">
                    <h5>مرحباً بك في نظام إدارة الموظفين</h5>
                    <p>هذا النظام يساعدك في إدارة:</p>
                    <ul class="text-start">
                        <li>بيانات الموظفين الشخصية والوظيفية</li>
                        <li>الدورات التدريبية والتطويرية</li>
                        <li>الإجازات وطلبات الموظفين</li>
                        <li>الإيفادات والمهام الخارجية</li>
                        <li>مكتبة الشركة ونظام الاستعارة</li>
                        <li>التنبيهات والإشعارات</li>
                        <li>التقارير والإحصائيات</li>
                    </ul>
                    <p><strong>للبدء:</strong> انقر على "إضافة موظف جديد" في تبويب إدارة الموظفين</p>
                </div>
            `, 'userGuide');
        }

        function showAbout() {
            showModal('حول النظام', `
                <div class="text-center">
                    <h4>نظام إدارة الموظفين</h4>
                    <p class="text-muted">الإصدار 1.0.0</p>
                    <hr>
                    <p>نظام شامل لإدارة الموارد البشرية مصمم خصيصاً للشركات والمؤسسات العربية</p>
                    <div class="row text-center mt-4">
                        <div class="col-md-4">
                            <i class="bi bi-shield-check fs-1 text-success"></i>
                            <h6>آمن وموثوق</h6>
                        </div>
                        <div class="col-md-4">
                            <i class="bi bi-speedometer2 fs-1 text-primary"></i>
                            <h6>سريع وفعال</h6>
                        </div>
                        <div class="col-md-4">
                            <i class="bi bi-phone fs-1 text-info"></i>
                            <h6>متجاوب</h6>
                        </div>
                    </div>
                    <hr>
                    <small class="text-muted">© 2024 جميع الحقوق محفوظة</small>
                </div>
            `, 'about');
        }

        function checkForUpdates() {
            showAlert('جاري فحص التحديثات...', 'info');
            setTimeout(() => {
                showAlert('النظام محدث إلى أحدث إصدار!', 'success');
            }, 2000);
        }

        function loadSavedSettings() {
            const savedSettings = localStorage.getItem('employeeSystemSettings');
            if (savedSettings) {
                try {
                    const settings = JSON.parse(savedSettings);

                    // تحميل معلومات الشركة
                    if (settings.companyName) document.getElementById('companyName').value = settings.companyName;
                    if (settings.companyEmail) document.getElementById('companyEmail').value = settings.companyEmail;
                    if (settings.companyPhone) document.getElementById('companyPhone').value = settings.companyPhone;
                    if (settings.companyAddress) document.getElementById('companyAddress').value = settings.companyAddress;

                    // تحميل إعدادات النظام
                    document.getElementById('darkMode').checked = settings.darkMode || false;
                    document.getElementById('notifications').checked = settings.notifications !== false;
                    document.getElementById('autoSave').checked = settings.autoSave !== false;
                    document.getElementById('soundEffects').checked = settings.soundEffects !== false;
                    if (settings.language) document.getElementById('language').value = settings.language;

                    // تحميل إعدادات الطباعة
                    document.getElementById('printHeader').checked = settings.printHeader !== false;
                    document.getElementById('printDate').checked = settings.printDate !== false;
                    document.getElementById('printLogo').checked = settings.printLogo || false;
                    if (settings.printFontSize) document.getElementById('printFontSize').value = settings.printFontSize;

                    // تحميل إعدادات الألوان
                    if (settings.primaryColor) document.getElementById('primaryColor').value = settings.primaryColor;
                    if (settings.successColor) document.getElementById('successColor').value = settings.successColor;
                    if (settings.warningColor) document.getElementById('warningColor').value = settings.warningColor;
                    if (settings.dangerColor) document.getElementById('dangerColor').value = settings.dangerColor;

                    // تحميل إعدادات الخط
                    if (settings.fontFamily) document.getElementById('fontFamily').value = settings.fontFamily;
                    if (settings.baseFontSize) document.getElementById('baseFontSize').value = settings.baseFontSize;

                    // تطبيق الإعدادات
                    applySettings(settings);

                    // تحديث معلومات النظام
                    updateSystemInfo();

                } catch (error) {
                    console.error('خطأ في تحميل الإعدادات:', error);
                    loadDefaultSettings();
                }
            } else {
                loadDefaultSettings();
            }
        }

        function updateSystemInfo() {
            // تحديث تاريخ آخر تحديث
            const now = new Date();
            const lastUpdateElement = document.getElementById('lastUpdate');
            if (lastUpdateElement) {
                lastUpdateElement.textContent = now.toLocaleDateString('ar-SA');
            }

            // تحديث عدد الموظفين
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const totalEmployeesElement = document.getElementById('totalEmployees');
            if (totalEmployeesElement) {
                totalEmployeesElement.textContent = employees.length;
            }

            // تحديث حجم البيانات
            const allData = localStorage.getItem('employeeSystemSettings') || '';
            const dataSize = new Blob([allData]).size;
            const dataSizeElement = document.getElementById('dataSize');
            if (dataSizeElement) {
                if (dataSize < 1024) {
                    dataSizeElement.textContent = dataSize + ' B';
                } else if (dataSize < 1024 * 1024) {
                    dataSizeElement.textContent = Math.round(dataSize / 1024) + ' KB';
                } else {
                    dataSizeElement.textContent = Math.round(dataSize / (1024 * 1024)) + ' MB';
                }
            }
        }

        // وظائف إدارة الكتب
        function addNewBook() {
            const bookForm = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">رقم الكتاب</label>
                        <input type="text" class="form-control" id="bookNumber" placeholder="أدخل رقم الكتاب" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ الكتاب</label>
                        <input type="date" class="form-control" id="bookDate" required>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">الموضوع</label>
                        <input type="text" class="form-control" id="bookSubject" placeholder="موضوع الكتاب" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الجهة الوارد منها</label>
                        <input type="text" class="form-control" id="bookSource" placeholder="اسم الجهة" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الكتاب</label>
                        <input type="text" class="form-control" id="bookTitle" placeholder="عنوان الكتاب" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الأضبارة</label>
                        <input type="text" class="form-control" id="bookFolder" placeholder="رقم أو اسم الأضبارة">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">المرفقات</label>
                        <select class="form-select" id="bookAttachments">
                            <option value="">بدون مرفقات</option>
                            <option value="وثائق">وثائق</option>
                            <option value="صور">صور</option>
                            <option value="ملفات إلكترونية">ملفات إلكترونية</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">الملاحظات</label>
                        <textarea class="form-control" id="bookNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
            `;

            showModal('إضافة كتاب جديد للأرشيف', bookForm, 'saveBook');
        }

        function saveBook() {
            const bookNumber = document.getElementById('bookNumber').value.trim();
            const bookDate = document.getElementById('bookDate').value;
            const bookSubject = document.getElementById('bookSubject').value.trim();
            const bookSource = document.getElementById('bookSource').value.trim();
            const bookTitle = document.getElementById('bookTitle').value.trim();
            const bookFolder = document.getElementById('bookFolder').value.trim();
            const bookAttachments = document.getElementById('bookAttachments').value;
            const bookNotes = document.getElementById('bookNotes').value.trim();

            // التحقق من البيانات المطلوبة
            if (!bookNumber || !bookDate || !bookSubject || !bookSource || !bookTitle) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // إنشاء كائن الكتاب
            const newBook = {
                id: Date.now(),
                number: bookNumber,
                date: bookDate,
                subject: bookSubject,
                source: bookSource,
                title: bookTitle,
                folder: bookFolder || '-',
                attachments: bookAttachments || 'بدون مرفقات',
                notes: bookNotes || '-',
                createdAt: new Date().toISOString()
            };

            // حفظ في Local Storage
            const books = JSON.parse(localStorage.getItem('books') || '[]');
            books.push(newBook);
            localStorage.setItem('books', JSON.stringify(books));

            // إغلاق النموذج وإظهار رسالة نجاح
            const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
            if (modal) modal.hide();

            showAlert('تم إضافة الكتاب للأرشيف بنجاح!', 'success');

            // تحديث الجدول إذا كان في تبويب الكتب
            updateBooksTable();
        }

        function updateBooksTable() {
            // هذه الوظيفة ستحديث جدول الكتب عند إضافة بيانات جديدة
            // يمكن تطويرها لاحقاً لعرض البيانات الفعلية
            console.log('تحديث جدول الكتب...');
        }

        function exportBooks() {
            showAlert('جاري تصدير بيانات الكتب...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الكتب بنجاح!', 'success');
            }, 2000);
        }

        function printBooks() {
            showAlert('جاري تحضير طباعة الكتب...', 'info');
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        function editBook(bookId, bookTitle) {
            showAlert(`تعديل الكتاب: ${bookTitle}`, 'info');
        }

        function deleteBook(bookId, bookTitle) {
            if (confirm(`هل أنت متأكد من حذف الكتاب: ${bookTitle}؟`)) {
                showAlert(`تم حذف الكتاب: ${bookTitle}`, 'success');
            }
        }

        function showArchiveStats() {
            const books = JSON.parse(localStorage.getItem('books') || '[]');
            const totalBooks = books.length;

            // حساب إحصائيات الشهر والأسبوع
            const now = new Date();
            const thisMonth = books.filter(book => {
                const bookDate = new Date(book.createdAt);
                return bookDate.getMonth() === now.getMonth() && bookDate.getFullYear() === now.getFullYear();
            }).length;

            const thisWeek = books.filter(book => {
                const bookDate = new Date(book.createdAt);
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                return bookDate >= weekAgo;
            }).length;

            // إحصائيات حسب الجهات
            const sourceStats = {};
            books.forEach(book => {
                sourceStats[book.source] = (sourceStats[book.source] || 0) + 1;
            });

            const statsContent = `
                <div class="row text-center mb-4">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h3 class="text-primary">${totalBooks}</h3>
                                <p class="mb-0">إجمالي الكتب</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body">
                                <h3 class="text-info">${thisMonth}</h3>
                                <p class="mb-0">هذا الشهر</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-body">
                                <h3 class="text-warning">${thisWeek}</h3>
                                <p class="mb-0">هذا الأسبوع</p>
                            </div>
                        </div>
                    </div>
                </div>

                <h6 class="mb-3">إحصائيات حسب الجهات:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الجهة</th>
                                <th>عدد الكتب</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(sourceStats).map(([source, count]) => `
                                <tr>
                                    <td>${source}</td>
                                    <td><span class="badge bg-primary">${count}</span></td>
                                    <td>${totalBooks > 0 ? Math.round((count / totalBooks) * 100) : 0}%</td>
                                </tr>
                            `).join('')}
                            ${Object.keys(sourceStats).length === 0 ? '<tr><td colspan="3" class="text-center text-muted">لا توجد بيانات</td></tr>' : ''}
                        </tbody>
                    </table>
                </div>
            `;

            showModal('تفاصيل إحصائيات الأرشيف', statsContent, null);
        }

        // وظائف إدارة الموظفين
        function addNewEmployee() {
            const employeeForm = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-hash text-primary"></i>
                            رقم الشركة
                        </label>
                        <input type="text" class="form-control" id="empCompanyNumber" placeholder="EMP001">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-person-fill text-success"></i>
                            الاسم الكامل
                        </label>
                        <input type="text" class="form-control" id="empFullName" placeholder="الاسم الثلاثي">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-briefcase-fill text-info"></i>
                            المسمى الوظيفي
                        </label>
                        <input type="text" class="form-control" id="empJobTitle" placeholder="المنصب الوظيفي">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-geo-alt-fill text-warning"></i>
                            مكان العمل
                        </label>
                        <input type="text" class="form-control" id="empWorkLocation" placeholder="أدخل مكان العمل">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-calendar-check-fill text-success"></i>
                            تاريخ بداية العمل
                        </label>
                        <input type="date" class="form-control" id="empStartDate">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-cake2-fill text-danger"></i>
                            تاريخ الميلاد
                        </label>
                        <input type="date" class="form-control" id="empBirthDate">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-phone-fill text-primary"></i>
                            رقم الجوال
                        </label>
                        <input type="tel" class="form-control" id="empMobile" placeholder="05xxxxxxxx">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-cash-coin text-success"></i>
                            الراتب (اختياري)
                        </label>
                        <input type="number" class="form-control" id="empSalary" placeholder="0" min="0">
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-house-fill text-info"></i>
                            عنوان السكن
                        </label>
                        <textarea class="form-control" id="empAddress" rows="2" placeholder="العنوان الكامل"></textarea>
                    </div>
                </div>
            `;

            showModal('إضافة موظف جديد', employeeForm, 'saveEmployee');
        }

        function saveEmployee() {
            const empCompanyNumber = document.getElementById('empCompanyNumber').value.trim();
            const empFullName = document.getElementById('empFullName').value.trim();
            const empJobTitle = document.getElementById('empJobTitle').value.trim();
            const empWorkLocation = document.getElementById('empWorkLocation').value;
            const empStartDate = document.getElementById('empStartDate').value;
            const empBirthDate = document.getElementById('empBirthDate').value;
            const empMobile = document.getElementById('empMobile').value.trim();
            const empSalary = document.getElementById('empSalary').value;
            const empAddress = document.getElementById('empAddress').value.trim();

            // التحقق من البيانات الأساسية فقط
            if (!empCompanyNumber && !empFullName) {
                showAlert('يرجى ملء رقم الشركة أو الاسم الكامل على الأقل', 'warning');
                return;
            }

            // التحقق من رقم الجوال
            const mobileRegex = /^05[0-9]{8}$/;
            if (!mobileRegex.test(empMobile)) {
                showAlert('يرجى إدخال رقم جوال صحيح (05xxxxxxxx)', 'warning');
                return;
            }

            // التحقق من عدم تكرار رقم الشركة
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            if (employees.some(emp => emp.companyNumber === empCompanyNumber)) {
                showAlert('رقم الشركة موجود مسبقاً، يرجى اختيار رقم آخر', 'warning');
                return;
            }

            // إنشاء كائن الموظف
            const newEmployee = {
                id: Date.now(),
                companyNumber: empCompanyNumber,
                fullName: empFullName,
                jobTitle: empJobTitle,
                workLocation: empWorkLocation,
                startDate: empStartDate,
                birthDate: empBirthDate,
                mobile: empMobile,
                salary: empSalary || 0,
                address: empAddress,
                createdAt: new Date().toISOString()
            };

            // حفظ في Local Storage
            employees.push(newEmployee);
            localStorage.setItem('employees', JSON.stringify(employees));

            // إغلاق النموذج وإظهار رسالة نجاح
            const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
            if (modal) modal.hide();

            showAlert('تم إضافة الموظف بنجاح!', 'success');

            // تحديث الجدول والإحصائيات
            updateEmployeesTable();
            updateSystemInfo();
        }

        function updateEmployeesTable() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const tbody = document.querySelector('#employees tbody');

            if (tbody && employees.length > 0) {
                tbody.innerHTML = employees.map((emp, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${emp.companyNumber}</td>
                        <td>${emp.fullName}</td>
                        <td>${emp.jobTitle}</td>
                        <td>${emp.workLocation}</td>
                        <td>${new Date(emp.startDate).toLocaleDateString('ar-SA')}</td>
                        <td>${emp.address}</td>
                        <td>${new Date(emp.birthDate).toLocaleDateString('ar-SA')}</td>
                        <td>${emp.mobile}</td>
                        <td class="actions-column">
                            <button class="btn btn-sm btn-warning me-1" onclick="editEmployee('${emp.id}', '${emp.fullName}')">
                                <i class="bi bi-pencil-fill"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteEmployee('${emp.id}', '${emp.fullName}')">
                                <i class="bi bi-trash-fill"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }
        }

        function editEmployee(empId, empName) {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const employee = employees.find(emp => emp.id == empId);

            if (!employee) {
                showAlert('لم يتم العثور على الموظف', 'error');
                return;
            }

            const employeeForm = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-hash text-primary"></i>
                            رقم الشركة
                        </label>
                        <input type="text" class="form-control" id="empCompanyNumber" value="${employee.companyNumber}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-person-fill text-success"></i>
                            الاسم الكامل
                        </label>
                        <input type="text" class="form-control" id="empFullName" value="${employee.fullName}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-briefcase-fill text-info"></i>
                            المسمى الوظيفي
                        </label>
                        <input type="text" class="form-control" id="empJobTitle" value="${employee.jobTitle}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-geo-alt-fill text-warning"></i>
                            مكان العمل
                        </label>
                        <input type="text" class="form-control" id="empWorkLocation" value="${employee.workLocation}" placeholder="أدخل مكان العمل">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-calendar-check-fill text-success"></i>
                            تاريخ بداية العمل
                        </label>
                        <input type="date" class="form-control" id="empStartDate" value="${employee.startDate}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-cake2-fill text-danger"></i>
                            تاريخ الميلاد
                        </label>
                        <input type="date" class="form-control" id="empBirthDate" value="${employee.birthDate}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-phone-fill text-primary"></i>
                            رقم الجوال
                        </label>
                        <input type="tel" class="form-control" id="empMobile" value="${employee.mobile}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-cash-coin text-success"></i>
                            الراتب (اختياري)
                        </label>
                        <input type="number" class="form-control" id="empSalary" value="${employee.salary || 0}" min="0">
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-house-fill text-info"></i>
                            عنوان السكن
                        </label>
                        <textarea class="form-control" id="empAddress" rows="2">${employee.address}</textarea>
                    </div>
                    <input type="hidden" id="empId" value="${employee.id}">
                </div>
            `;

            showModal('تعديل بيانات الموظف', employeeForm, 'updateEmployee');
        }

        function updateEmployee() {
            const empId = document.getElementById('empId').value;
            const empCompanyNumber = document.getElementById('empCompanyNumber').value.trim();
            const empFullName = document.getElementById('empFullName').value.trim();
            const empJobTitle = document.getElementById('empJobTitle').value.trim();
            const empWorkLocation = document.getElementById('empWorkLocation').value;
            const empStartDate = document.getElementById('empStartDate').value;
            const empBirthDate = document.getElementById('empBirthDate').value;
            const empMobile = document.getElementById('empMobile').value.trim();
            const empSalary = document.getElementById('empSalary').value;
            const empAddress = document.getElementById('empAddress').value.trim();

            // التحقق من البيانات المطلوبة
            if (!empCompanyNumber || !empFullName || !empJobTitle || !empWorkLocation || !empStartDate || !empBirthDate || !empMobile || !empAddress) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // التحقق من رقم الجوال
            const mobileRegex = /^05[0-9]{8}$/;
            if (!mobileRegex.test(empMobile)) {
                showAlert('يرجى إدخال رقم جوال صحيح (05xxxxxxxx)', 'warning');
                return;
            }

            // تحديث بيانات الموظف
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const empIndex = employees.findIndex(emp => emp.id == empId);

            if (empIndex !== -1) {
                employees[empIndex] = {
                    ...employees[empIndex],
                    companyNumber: empCompanyNumber,
                    fullName: empFullName,
                    jobTitle: empJobTitle,
                    workLocation: empWorkLocation,
                    startDate: empStartDate,
                    birthDate: empBirthDate,
                    mobile: empMobile,
                    salary: empSalary || 0,
                    address: empAddress,
                    updatedAt: new Date().toISOString()
                };

                localStorage.setItem('employees', JSON.stringify(employees));

                // إغلاق النموذج وإظهار رسالة نجاح
                const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
                if (modal) modal.hide();

                showAlert('تم تحديث بيانات الموظف بنجاح!', 'success');

                // تحديث الجدول
                updateEmployeesTable();
            }
        }

        function deleteEmployee(empId, empName) {
            if (confirm(`هل أنت متأكد من حذف الموظف: ${empName}؟\n\nسيتم حذف جميع البيانات المرتبطة بهذا الموظف نهائياً.`)) {
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');
                const filteredEmployees = employees.filter(emp => emp.id != empId);

                localStorage.setItem('employees', JSON.stringify(filteredEmployees));

                showAlert(`تم حذف الموظف: ${empName} بنجاح`, 'success');

                // تحديث الجدول والإحصائيات
                updateEmployeesTable();
                updateSystemInfo();
            }
        }

        // وظائف إدارة الدورات التدريبية
        function addNewCourse() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');

            if (employees.length === 0) {
                showAlert('يجب إضافة موظفين أولاً قبل تسجيلهم في الدورات', 'warning');
                return;
            }

            const courseForm = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اختيار الموظف</label>
                        <select class="form-select" id="courseEmployeeId" required>
                            <option value="">اختر الموظف</option>
                            ${employees.map(emp => `
                                <option value="${emp.id}">${emp.fullName} - ${emp.companyNumber}</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم الدورة</label>
                        <input type="text" class="form-control" id="courseName" placeholder="اسم الدورة التدريبية" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">نوع الدورة</label>
                        <select class="form-select" id="courseType" required>
                            <option value="">اختر النوع</option>
                            <option value="تقنية">تقنية</option>
                            <option value="إدارية">إدارية</option>
                            <option value="مالية">مالية</option>
                            <option value="تسويقية">تسويقية</option>
                            <option value="قانونية">قانونية</option>
                            <option value="صحة وسلامة">صحة وسلامة</option>
                            <option value="تطوير ذاتي">تطوير ذاتي</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">مدة الدورة</label>
                        <input type="text" class="form-control" id="courseDuration" placeholder="مثال: 5 أيام، 3 أسابيع" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ البداية</label>
                        <input type="date" class="form-control" id="courseStartDate" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ النهاية</label>
                        <input type="date" class="form-control" id="courseEndDate" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">مكان الدورة</label>
                        <input type="text" class="form-control" id="courseLocation" placeholder="مكان انعقاد الدورة" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">حالة الدورة</label>
                        <select class="form-select" id="courseStatus">
                            <option value="مخططة">مخططة</option>
                            <option value="قيد التنفيذ">قيد التنفيذ</option>
                            <option value="مكتملة">مكتملة</option>
                            <option value="ملغية">ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">ملاحظات</label>
                        <textarea class="form-control" id="courseNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
            `;

            showModal('إضافة دورة تدريبية جديدة', courseForm, 'saveCourse');
        }

        function saveCourse() {
            const courseEmployeeId = document.getElementById('courseEmployeeId').value;
            const courseName = document.getElementById('courseName').value.trim();
            const courseType = document.getElementById('courseType').value;
            const courseDuration = document.getElementById('courseDuration').value.trim();
            const courseStartDate = document.getElementById('courseStartDate').value;
            const courseEndDate = document.getElementById('courseEndDate').value;
            const courseLocation = document.getElementById('courseLocation').value.trim();
            const courseStatus = document.getElementById('courseStatus').value;
            const courseNotes = document.getElementById('courseNotes').value.trim();

            // التحقق من البيانات المطلوبة
            if (!courseEmployeeId || !courseName || !courseType || !courseDuration || !courseStartDate || !courseEndDate || !courseLocation) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // التحقق من صحة التواريخ
            if (new Date(courseStartDate) > new Date(courseEndDate)) {
                showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
                return;
            }

            // الحصول على بيانات الموظف
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const employee = employees.find(emp => emp.id == courseEmployeeId);

            if (!employee) {
                showAlert('لم يتم العثور على الموظف المحدد', 'error');
                return;
            }

            // إنشاء كائن الدورة
            const newCourse = {
                id: Date.now(),
                employeeId: courseEmployeeId,
                employeeName: employee.fullName,
                employeeNumber: employee.companyNumber,
                employeeJobTitle: employee.jobTitle,
                courseName: courseName,
                courseType: courseType,
                duration: courseDuration,
                startDate: courseStartDate,
                endDate: courseEndDate,
                location: courseLocation,
                status: courseStatus,
                notes: courseNotes || '-',
                createdAt: new Date().toISOString()
            };

            // حفظ في Local Storage
            const courses = JSON.parse(localStorage.getItem('courses') || '[]');
            courses.push(newCourse);
            localStorage.setItem('courses', JSON.stringify(courses));

            // إغلاق النموذج وإظهار رسالة نجاح
            const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
            if (modal) modal.hide();

            showAlert('تم إضافة الدورة التدريبية بنجاح!', 'success');

            // تحديث الجدول
            updateCoursesTable();
        }

        function updateCoursesTable() {
            const courses = JSON.parse(localStorage.getItem('courses') || '[]');
            const tbody = document.querySelector('#courses tbody');

            if (tbody && courses.length > 0) {
                tbody.innerHTML = courses.map((course, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${course.employeeNumber}</td>
                        <td>${course.employeeName}</td>
                        <td>${course.employeeJobTitle}</td>
                        <td>${course.courseName}</td>
                        <td><span class="badge bg-info">${course.courseType}</span></td>
                        <td>${course.duration}</td>
                        <td>${course.location}</td>
                        <td class="actions-column">
                            <button class="btn btn-sm btn-warning me-1" onclick="editCourse('${course.id}', '${course.courseName}')">
                                <i class="bi bi-pencil-fill"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteCourse('${course.id}', '${course.courseName}')">
                                <i class="bi bi-trash-fill"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }
        }

        function editCourse(courseId, courseName) {
            const courses = JSON.parse(localStorage.getItem('courses') || '[]');
            const course = courses.find(c => c.id == courseId);
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');

            if (!course) {
                showAlert('لم يتم العثور على الدورة', 'error');
                return;
            }

            const courseForm = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اختيار الموظف</label>
                        <select class="form-select" id="courseEmployeeId" required>
                            ${employees.map(emp => `
                                <option value="${emp.id}" ${emp.id == course.employeeId ? 'selected' : ''}>${emp.fullName} - ${emp.companyNumber}</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم الدورة</label>
                        <input type="text" class="form-control" id="courseName" value="${course.courseName}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">نوع الدورة</label>
                        <select class="form-select" id="courseType" required>
                            <option value="تقنية" ${course.courseType === 'تقنية' ? 'selected' : ''}>تقنية</option>
                            <option value="إدارية" ${course.courseType === 'إدارية' ? 'selected' : ''}>إدارية</option>
                            <option value="مالية" ${course.courseType === 'مالية' ? 'selected' : ''}>مالية</option>
                            <option value="تسويقية" ${course.courseType === 'تسويقية' ? 'selected' : ''}>تسويقية</option>
                            <option value="قانونية" ${course.courseType === 'قانونية' ? 'selected' : ''}>قانونية</option>
                            <option value="صحة وسلامة" ${course.courseType === 'صحة وسلامة' ? 'selected' : ''}>صحة وسلامة</option>
                            <option value="تطوير ذاتي" ${course.courseType === 'تطوير ذاتي' ? 'selected' : ''}>تطوير ذاتي</option>
                            <option value="أخرى" ${course.courseType === 'أخرى' ? 'selected' : ''}>أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">مدة الدورة</label>
                        <input type="text" class="form-control" id="courseDuration" value="${course.duration}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ البداية</label>
                        <input type="date" class="form-control" id="courseStartDate" value="${course.startDate}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ النهاية</label>
                        <input type="date" class="form-control" id="courseEndDate" value="${course.endDate}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">مكان الدورة</label>
                        <input type="text" class="form-control" id="courseLocation" value="${course.location}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">حالة الدورة</label>
                        <select class="form-select" id="courseStatus">
                            <option value="مخططة" ${course.status === 'مخططة' ? 'selected' : ''}>مخططة</option>
                            <option value="قيد التنفيذ" ${course.status === 'قيد التنفيذ' ? 'selected' : ''}>قيد التنفيذ</option>
                            <option value="مكتملة" ${course.status === 'مكتملة' ? 'selected' : ''}>مكتملة</option>
                            <option value="ملغية" ${course.status === 'ملغية' ? 'selected' : ''}>ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">ملاحظات</label>
                        <textarea class="form-control" id="courseNotes" rows="3">${course.notes === '-' ? '' : course.notes}</textarea>
                    </div>
                    <input type="hidden" id="courseId" value="${course.id}">
                </div>
            `;

            showModal('تعديل الدورة التدريبية', courseForm, 'updateCourse');
        }

        function updateCourse() {
            const courseId = document.getElementById('courseId').value;
            const courseEmployeeId = document.getElementById('courseEmployeeId').value;
            const courseName = document.getElementById('courseName').value.trim();
            const courseType = document.getElementById('courseType').value;
            const courseDuration = document.getElementById('courseDuration').value.trim();
            const courseStartDate = document.getElementById('courseStartDate').value;
            const courseEndDate = document.getElementById('courseEndDate').value;
            const courseLocation = document.getElementById('courseLocation').value.trim();
            const courseStatus = document.getElementById('courseStatus').value;
            const courseNotes = document.getElementById('courseNotes').value.trim();

            // التحقق من البيانات المطلوبة
            if (!courseEmployeeId || !courseName || !courseType || !courseDuration || !courseStartDate || !courseEndDate || !courseLocation) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // التحقق من صحة التواريخ
            if (new Date(courseStartDate) > new Date(courseEndDate)) {
                showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
                return;
            }

            // الحصول على بيانات الموظف
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const employee = employees.find(emp => emp.id == courseEmployeeId);

            if (!employee) {
                showAlert('لم يتم العثور على الموظف المحدد', 'error');
                return;
            }

            // تحديث بيانات الدورة
            const courses = JSON.parse(localStorage.getItem('courses') || '[]');
            const courseIndex = courses.findIndex(c => c.id == courseId);

            if (courseIndex !== -1) {
                courses[courseIndex] = {
                    ...courses[courseIndex],
                    employeeId: courseEmployeeId,
                    employeeName: employee.fullName,
                    employeeNumber: employee.companyNumber,
                    employeeJobTitle: employee.jobTitle,
                    courseName: courseName,
                    courseType: courseType,
                    duration: courseDuration,
                    startDate: courseStartDate,
                    endDate: courseEndDate,
                    location: courseLocation,
                    status: courseStatus,
                    notes: courseNotes || '-',
                    updatedAt: new Date().toISOString()
                };

                localStorage.setItem('courses', JSON.stringify(courses));

                // إغلاق النموذج وإظهار رسالة نجاح
                const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
                if (modal) modal.hide();

                showAlert('تم تحديث الدورة التدريبية بنجاح!', 'success');

                // تحديث الجدول
                updateCoursesTable();
            }
        }

        function deleteCourse(courseId, courseName) {
            if (confirm(`هل أنت متأكد من حذف الدورة: ${courseName}؟`)) {
                const courses = JSON.parse(localStorage.getItem('courses') || '[]');
                const filteredCourses = courses.filter(c => c.id != courseId);

                localStorage.setItem('courses', JSON.stringify(filteredCourses));

                showAlert(`تم حذف الدورة: ${courseName} بنجاح`, 'success');

                // تحديث الجدول
                updateCoursesTable();
            }
        }

        function exportCourses() {
            showAlert('جاري تصدير بيانات الدورات...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الدورات بنجاح!', 'success');
            }, 2000);
        }

        function printCourses() {
            showAlert('جاري تحضير طباعة الدورات...', 'info');
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        // وظائف إدارة الإجازات
        function addNewLeave() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');

            if (employees.length === 0) {
                showAlert('يجب إضافة موظفين أولاً قبل إضافة إجازات', 'warning');
                return;
            }

            const leaveForm = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اختيار الموظف</label>
                        <select class="form-select" id="leaveEmployeeId" required>
                            <option value="">اختر الموظف</option>
                            ${employees.map(emp => `
                                <option value="${emp.id}">${emp.fullName} - ${emp.companyNumber}</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">نوع الإجازة</label>
                        <select class="form-select" id="leaveType" required>
                            <option value="">اختر النوع</option>
                            <option value="سنوية">سنوية</option>
                            <option value="مرضية">مرضية</option>
                            <option value="طارئة">طارئة</option>
                            <option value="أمومة">أمومة</option>
                            <option value="حج وعمرة">حج وعمرة</option>
                            <option value="بدون راتب">بدون راتب</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ بداية الإجازة</label>
                        <input type="date" class="form-control" id="leaveStartDate" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ نهاية الإجازة</label>
                        <input type="date" class="form-control" id="leaveEndDate" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">عدد الأيام</label>
                        <input type="number" class="form-control" id="leaveDays" min="1" placeholder="سيتم حسابه تلقائياً" readonly>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">حالة الإجازة</label>
                        <select class="form-select" id="leaveStatus">
                            <option value="معلقة">معلقة</option>
                            <option value="موافق عليها">موافق عليها</option>
                            <option value="مرفوضة">مرفوضة</option>
                            <option value="منتهية">منتهية</option>
                        </select>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">سبب الإجازة</label>
                        <textarea class="form-control" id="leaveReason" rows="3" placeholder="سبب طلب الإجازة" required></textarea>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">ملاحظات</label>
                        <textarea class="form-control" id="leaveNotes" rows="2" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
            `;

            showModal('إضافة إجازة جديدة', leaveForm, 'saveLeave');

            // إضافة مستمع لحساب عدد الأيام تلقائياً
            setTimeout(() => {
                const startDateInput = document.getElementById('leaveStartDate');
                const endDateInput = document.getElementById('leaveEndDate');
                const daysInput = document.getElementById('leaveDays');

                function calculateDays() {
                    if (startDateInput.value && endDateInput.value) {
                        const startDate = new Date(startDateInput.value);
                        const endDate = new Date(endDateInput.value);
                        const diffTime = Math.abs(endDate - startDate);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                        daysInput.value = diffDays > 0 ? diffDays : 0;
                    }
                }

                startDateInput.addEventListener('change', calculateDays);
                endDateInput.addEventListener('change', calculateDays);
            }, 100);
        }

        function saveLeave() {
            const leaveEmployeeId = document.getElementById('leaveEmployeeId').value;
            const leaveType = document.getElementById('leaveType').value;
            const leaveStartDate = document.getElementById('leaveStartDate').value;
            const leaveEndDate = document.getElementById('leaveEndDate').value;
            const leaveDays = document.getElementById('leaveDays').value;
            const leaveStatus = document.getElementById('leaveStatus').value;
            const leaveReason = document.getElementById('leaveReason').value.trim();
            const leaveNotes = document.getElementById('leaveNotes').value.trim();

            // التحقق من البيانات المطلوبة
            if (!leaveEmployeeId || !leaveType || !leaveStartDate || !leaveEndDate || !leaveReason) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // التحقق من صحة التواريخ
            if (new Date(leaveStartDate) > new Date(leaveEndDate)) {
                showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
                return;
            }

            // الحصول على بيانات الموظف
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const employee = employees.find(emp => emp.id == leaveEmployeeId);

            if (!employee) {
                showAlert('لم يتم العثور على الموظف المحدد', 'error');
                return;
            }

            // إنشاء كائن الإجازة
            const newLeave = {
                id: Date.now(),
                employeeId: leaveEmployeeId,
                employeeName: employee.fullName,
                employeeNumber: employee.companyNumber,
                employeeJobTitle: employee.jobTitle,
                employeeWorkLocation: employee.workLocation,
                leaveType: leaveType,
                startDate: leaveStartDate,
                endDate: leaveEndDate,
                days: leaveDays,
                status: leaveStatus,
                reason: leaveReason,
                notes: leaveNotes || '-',
                createdAt: new Date().toISOString()
            };

            // حفظ في Local Storage
            const leaves = JSON.parse(localStorage.getItem('leaves') || '[]');
            leaves.push(newLeave);
            localStorage.setItem('leaves', JSON.stringify(leaves));

            // إغلاق النموذج وإظهار رسالة نجاح
            const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
            if (modal) modal.hide();

            showAlert('تم إضافة الإجازة بنجاح!', 'success');

            // تحديث الجدول
            updateLeavesTable();
        }

        function updateLeavesTable() {
            const leaves = JSON.parse(localStorage.getItem('leaves') || '[]');
            const tbody = document.querySelector('#leaves tbody');

            if (tbody && leaves.length > 0) {
                tbody.innerHTML = leaves.map((leave, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${leave.employeeNumber}</td>
                        <td>${leave.employeeName}</td>
                        <td>${leave.employeeJobTitle}</td>
                        <td>${leave.employeeWorkLocation}</td>
                        <td>${new Date(leave.startDate).toLocaleDateString('ar-SA')}</td>
                        <td><span class="badge bg-${leave.status === 'موافق عليها' ? 'success' : leave.status === 'مرفوضة' ? 'danger' : 'warning'}">${leave.days} ${leave.days == 1 ? 'يوم' : 'أيام'}</span></td>
                        <td class="actions-column">
                            <button class="btn btn-sm btn-warning me-1" onclick="editLeave('${leave.id}', '${leave.employeeName}')">
                                <i class="bi bi-pencil-fill"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteLeave('${leave.id}', '${leave.employeeName}')">
                                <i class="bi bi-trash-fill"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }
        }

        function editLeave(leaveId, employeeName) {
            showAlert(`تعديل إجازة: ${employeeName}`, 'info');
        }

        function deleteLeave(leaveId, employeeName) {
            if (confirm(`هل أنت متأكد من حذف إجازة: ${employeeName}؟`)) {
                const leaves = JSON.parse(localStorage.getItem('leaves') || '[]');
                const filteredLeaves = leaves.filter(l => l.id != leaveId);

                localStorage.setItem('leaves', JSON.stringify(filteredLeaves));

                showAlert(`تم حذف إجازة: ${employeeName} بنجاح`, 'success');

                // تحديث الجدول
                updateLeavesTable();
            }
        }

        function exportLeaves() {
            showAlert('جاري تصدير بيانات الإجازات...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الإجازات بنجاح!', 'success');
            }, 2000);
        }

        function printLeaves() {
            showAlert('جاري تحضير طباعة الإجازات...', 'info');
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        // وظائف إدارة الإيفادات
        function addNewDelegation() {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');

            if (employees.length === 0) {
                showAlert('يجب إضافة موظفين أولاً قبل إضافة إيفادات', 'warning');
                return;
            }

            const delegationForm = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اختيار الموظف</label>
                        <select class="form-select" id="delegationEmployeeId" required>
                            <option value="">اختر الموظف</option>
                            ${employees.map(emp => `
                                <option value="${emp.id}">${emp.fullName} - ${emp.companyNumber}</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">وجهة الإيفاد</label>
                        <input type="text" class="form-control" id="delegationDestination" placeholder="المدينة أو البلد" required>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">الغرض من الإيفاد</label>
                        <input type="text" class="form-control" id="delegationPurpose" placeholder="سبب أو هدف الإيفاد" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ البداية</label>
                        <input type="date" class="form-control" id="delegationStartDate" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ النهاية</label>
                        <input type="date" class="form-control" id="delegationEndDate" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">نوع الإيفاد</label>
                        <select class="form-select" id="delegationType" required>
                            <option value="">اختر النوع</option>
                            <option value="محلي">محلي</option>
                            <option value="دولي">دولي</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">حالة الإيفاد</label>
                        <select class="form-select" id="delegationStatus">
                            <option value="مخطط">مخطط</option>
                            <option value="قيد التنفيذ">قيد التنفيذ</option>
                            <option value="مكتمل">مكتمل</option>
                            <option value="ملغي">ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">التكلفة المتوقعة</label>
                        <input type="number" class="form-control" id="delegationCost" placeholder="0" min="0">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">جهة التمويل</label>
                        <select class="form-select" id="delegationFunding">
                            <option value="الشركة">الشركة</option>
                            <option value="جهة خارجية">جهة خارجية</option>
                            <option value="مشترك">مشترك</option>
                        </select>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">ملاحظات</label>
                        <textarea class="form-control" id="delegationNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
            `;

            showModal('إضافة إيفاد جديد', delegationForm, 'saveDelegation');
        }

        function saveDelegation() {
            const delegationEmployeeId = document.getElementById('delegationEmployeeId').value;
            const delegationDestination = document.getElementById('delegationDestination').value.trim();
            const delegationPurpose = document.getElementById('delegationPurpose').value.trim();
            const delegationStartDate = document.getElementById('delegationStartDate').value;
            const delegationEndDate = document.getElementById('delegationEndDate').value;
            const delegationType = document.getElementById('delegationType').value;
            const delegationStatus = document.getElementById('delegationStatus').value;
            const delegationCost = document.getElementById('delegationCost').value;
            const delegationFunding = document.getElementById('delegationFunding').value;
            const delegationNotes = document.getElementById('delegationNotes').value.trim();

            // التحقق من البيانات المطلوبة
            if (!delegationEmployeeId || !delegationDestination || !delegationPurpose || !delegationStartDate || !delegationEndDate || !delegationType) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // التحقق من صحة التواريخ
            if (new Date(delegationStartDate) > new Date(delegationEndDate)) {
                showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
                return;
            }

            // الحصول على بيانات الموظف
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const employee = employees.find(emp => emp.id == delegationEmployeeId);

            if (!employee) {
                showAlert('لم يتم العثور على الموظف المحدد', 'error');
                return;
            }

            // إنشاء كائن الإيفاد
            const newDelegation = {
                id: Date.now(),
                employeeId: delegationEmployeeId,
                employeeName: employee.fullName,
                employeeNumber: employee.companyNumber,
                destination: delegationDestination,
                purpose: delegationPurpose,
                startDate: delegationStartDate,
                endDate: delegationEndDate,
                type: delegationType,
                status: delegationStatus,
                cost: delegationCost || 0,
                funding: delegationFunding,
                notes: delegationNotes || '-',
                createdAt: new Date().toISOString()
            };

            // حفظ في Local Storage
            const delegations = JSON.parse(localStorage.getItem('delegations') || '[]');
            delegations.push(newDelegation);
            localStorage.setItem('delegations', JSON.stringify(delegations));

            // إغلاق النموذج وإظهار رسالة نجاح
            const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
            if (modal) modal.hide();

            showAlert('تم إضافة الإيفاد بنجاح!', 'success');

            // تحديث الجدول
            updateDelegationsTable();
        }

        function updateDelegationsTable() {
            const delegations = JSON.parse(localStorage.getItem('delegations') || '[]');
            const tbody = document.querySelector('#delegations tbody');

            if (tbody && delegations.length > 0) {
                tbody.innerHTML = delegations.map((delegation, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${delegation.employeeNumber}</td>
                        <td>${delegation.employeeName}</td>
                        <td>${delegation.destination}</td>
                        <td>${delegation.purpose}</td>
                        <td>${new Date(delegation.startDate).toLocaleDateString('ar-SA')}</td>
                        <td>${new Date(delegation.endDate).toLocaleDateString('ar-SA')}</td>
                        <td><span class="badge bg-${delegation.status === 'مكتمل' ? 'success' : delegation.status === 'قيد التنفيذ' ? 'warning' : 'info'}">${delegation.status}</span></td>
                    </tr>
                `).join('');
            }
        }

        function exportDelegations() {
            showAlert('جاري تصدير بيانات الإيفادات...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الإيفادات بنجاح!', 'success');
            }, 2000);
        }

        function printDelegations() {
            showAlert('جاري تحضير طباعة الإيفادات...', 'info');
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        // وظائف إضافية للإعدادات
        function uploadLogo() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/png,image/jpeg,image/jpg';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    if (file.size > 2 * 1024 * 1024) {
                        showAlert('حجم الملف كبير جداً. الحد الأقصى 2MB', 'warning');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        localStorage.setItem('companyLogo', e.target.result);
                        showAlert('تم رفع الشعار بنجاح!', 'success');
                        updateLogoDisplay();
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }

        function removeLogo() {
            if (confirm('هل أنت متأكد من حذف شعار الشركة؟')) {
                localStorage.removeItem('companyLogo');
                showAlert('تم حذف الشعار', 'info');
                updateLogoDisplay();
            }
        }

        function updateLogoDisplay() {
            const logoData = localStorage.getItem('companyLogo');
            const placeholder = document.querySelector('.logo-placeholder');

            if (logoData && placeholder) {
                placeholder.innerHTML = `<img src="${logoData}" alt="شعار الشركة" style="max-width: 100%; max-height: 100%; border-radius: 10px;">`;
            } else if (placeholder) {
                placeholder.innerHTML = '<i class="bi bi-image fs-1 text-muted"></i>';
            }
        }

        // تحديث أزرار الإجازات والإيفادات
        function showAddLeaveModal() {
            addNewLeave();
        }

        function showAddDelegationModal() {
            addNewDelegation();
        }

        function showAddEmployeeModal() {
            addNewEmployee();
        }

        function showAddCourseModal() {
            addNewCourse();
        }

        function exportEmployees() {
            showAlert('جاري تصدير بيانات الموظفين...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الموظفين بنجاح!', 'success');
            }, 2000);
        }

        // وظائف تسجيل الخروج والملف الشخصي
        function logout() {
            // إظهار نافذة تأكيد تسجيل الخروج
            const confirmLogout = confirm('هل أنت متأكد من تسجيل الخروج؟\n\nسيتم حفظ جميع البيانات تلقائياً.');

            if (confirmLogout) {
                // حفظ البيانات قبل الخروج
                saveAllData();

                // إظهار رسالة وداع
                showAlert('تم تسجيل الخروج بنجاح. شكراً لاستخدام النظام!', 'success');

                // تأخير قبل إعادة التوجيه
                setTimeout(() => {
                    // إنشاء صفحة تسجيل دخول بسيطة
                    document.body.innerHTML = `
                        <div class="login-container">
                            <div class="login-card">
                                <div class="login-header">
                                    <i class="bi bi-people-fill"></i>
                                    <h2>نظام إدارة الموظفين</h2>
                                    <p>تم تسجيل الخروج بنجاح</p>
                                </div>
                                <div class="login-body">
                                    <div class="mb-3">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="text" class="form-control" id="username" placeholder="أدخل اسم المستخدم">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" id="password" placeholder="أدخل كلمة المرور">
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="login()">
                                        <i class="bi bi-box-arrow-in-right me-2"></i>
                                        تسجيل الدخول
                                    </button>
                                </div>
                                <div class="login-footer">
                                    <small class="text-muted">
                                        للدخول السريع: اسم المستخدم: admin | كلمة المرور: 123
                                    </small>
                                </div>
                            </div>
                        </div>
                        <style>
                            .login-container {
                                min-height: 100vh;
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-family: 'Tajawal', sans-serif;
                            }
                            .login-card {
                                background: white;
                                border-radius: 20px;
                                padding: 2rem;
                                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
                                width: 100%;
                                max-width: 400px;
                                text-align: center;
                            }
                            .login-header i {
                                font-size: 3rem;
                                color: #667eea;
                                margin-bottom: 1rem;
                            }
                            .login-header h2 {
                                color: #333;
                                margin-bottom: 0.5rem;
                                font-weight: bold;
                            }
                            .login-header p {
                                color: #28a745;
                                margin-bottom: 2rem;
                            }
                            .login-body {
                                text-align: right;
                                margin-bottom: 1rem;
                            }
                            .form-control {
                                border-radius: 10px;
                                border: 2px solid #e9ecef;
                                padding: 0.75rem;
                            }
                            .form-control:focus {
                                border-color: #667eea;
                                box-shadow: 0 0 15px rgba(102, 126, 234, 0.2);
                            }
                            .btn-primary {
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                border: none;
                                border-radius: 10px;
                                padding: 0.75rem;
                                font-weight: bold;
                            }
                            .login-footer {
                                margin-top: 1rem;
                                padding-top: 1rem;
                                border-top: 1px solid #e9ecef;
                            }
                        </style>
                    `;
                }, 2000);
            }
        }

        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // تحقق بسيط من بيانات الدخول
            if ((username === 'admin' && password === '123') ||
                (username === 'user' && password === 'user') ||
                (username && password)) {

                // إعادة تحميل الصفحة للعودة للنظام
                location.reload();
            } else {
                alert('يرجى إدخال اسم المستخدم وكلمة المرور');
            }
        }

        function showUserProfile() {
            const profileForm = `
                <div class="row">
                    <div class="col-md-12 text-center mb-4">
                        <div class="profile-avatar">
                            <i class="bi bi-person-circle display-1 text-primary"></i>
                        </div>
                        <h5 class="mt-2">المدير العام</h5>
                        <p class="text-muted">مدير النظام</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-person-fill text-primary"></i>
                            الاسم الكامل
                        </label>
                        <input type="text" class="form-control" id="profileName" value="المدير العام" placeholder="أدخل الاسم الكامل">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-envelope-fill text-success"></i>
                            البريد الإلكتروني
                        </label>
                        <input type="email" class="form-control" id="profileEmail" value="<EMAIL>" placeholder="أدخل البريد الإلكتروني">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-phone-fill text-info"></i>
                            رقم الهاتف
                        </label>
                        <input type="tel" class="form-control" id="profilePhone" value="0501234567" placeholder="أدخل رقم الهاتف">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-briefcase-fill text-warning"></i>
                            المنصب
                        </label>
                        <input type="text" class="form-control" id="profilePosition" value="مدير النظام" placeholder="أدخل المنصب">
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-key-fill text-danger"></i>
                            تغيير كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="profilePassword" placeholder="كلمة المرور الجديدة (اختياري)">
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-chat-text-fill text-secondary"></i>
                            نبذة شخصية
                        </label>
                        <textarea class="form-control" id="profileBio" rows="3" placeholder="نبذة مختصرة عنك">مدير نظام إدارة الموظفين مع خبرة في إدارة البيانات والأنظمة الإدارية.</textarea>
                    </div>
                </div>
            `;

            showModal('الملف الشخصي', profileForm, 'saveProfile');
        }

        function saveProfile() {
            const profileData = {
                name: document.getElementById('profileName').value,
                email: document.getElementById('profileEmail').value,
                phone: document.getElementById('profilePhone').value,
                position: document.getElementById('profilePosition').value,
                password: document.getElementById('profilePassword').value,
                bio: document.getElementById('profileBio').value
            };

            // حفظ بيانات الملف الشخصي
            localStorage.setItem('userProfile', JSON.stringify(profileData));

            // إغلاق النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
            if (modal) modal.hide();

            showAlert('تم حفظ الملف الشخصي بنجاح!', 'success');
        }

        function saveAllData() {
            // حفظ جميع البيانات قبل تسجيل الخروج
            const allData = {
                employees: JSON.parse(localStorage.getItem('employees') || '[]'),
                courses: JSON.parse(localStorage.getItem('courses') || '[]'),
                leaves: JSON.parse(localStorage.getItem('leaves') || '[]'),
                delegations: JSON.parse(localStorage.getItem('delegations') || '[]'),
                books: JSON.parse(localStorage.getItem('books') || '[]'),
                settings: JSON.parse(localStorage.getItem('settings') || '{}'),
                userProfile: JSON.parse(localStorage.getItem('userProfile') || '{}'),
                lastSaved: new Date().toISOString()
            };

            localStorage.setItem('systemBackup', JSON.stringify(allData));
            console.log('تم حفظ جميع البيانات بنجاح');
        }

        // وظيفة عامة لإظهار النوافذ المنبثقة
        function showModal(title, content, action) {
            // تحديد نوع النموذج بناءً على العنوان
            let modalClass = '';
            let iconClass = '';

            if (title.includes('موظف')) {
                modalClass = 'employee-form';
                iconClass = 'bi-person-plus-fill';
            } else if (title.includes('دورة')) {
                modalClass = 'course-form';
                iconClass = 'bi-book-fill';
            } else if (title.includes('إجازة')) {
                modalClass = 'leave-form';
                iconClass = 'bi-calendar-check-fill';
            } else if (title.includes('إيفاد')) {
                modalClass = 'delegation-form';
                iconClass = 'bi-airplane-fill';
            } else if (title.includes('كتاب')) {
                modalClass = 'book-form';
                iconClass = 'bi-book-fill';
            }

            const modalHtml = `
                <div class="modal fade" id="dynamicModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content ${modalClass}">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi ${iconClass}"></i>
                                    ${title}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء
                                </button>
                                <button type="button" class="btn btn-primary" onclick="${action}()">
                                    <i class="bi bi-check-circle me-2"></i>
                                    حفظ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة أي modal موجود
            const existingModal = document.getElementById('dynamicModal');
            if (existingModal) {
                existingModal.remove();
            }

            // إضافة modal جديد
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إظهار modal
            const modal = new bootstrap.Modal(document.getElementById('dynamicModal'));
            modal.show();
        }

        function handleModalAction(action) {
            switch(action) {
                case 'addEmployee':
                    const employeeData = {
                        companyNumber: document.getElementById('companyNumber').value,
                        fullName: document.getElementById('fullName').value,
                        jobTitle: document.getElementById('jobTitle').value,
                        workLocation: document.getElementById('workLocation').value,
                        startDate: document.getElementById('startDate').value,
                        birthDate: document.getElementById('birthDate').value,
                        mobile: document.getElementById('mobile').value,
                        address: document.getElementById('address').value
                    };

                    if (validateEmployeeData(employeeData)) {
                        showAlert('تم إضافة الموظف بنجاح!', 'success');
                        bootstrap.Modal.getInstance(document.getElementById('dynamicModal')).hide();
                    }
                    break;

                case 'addCourse':
                    showAlert('تم إضافة الدورة التدريبية بنجاح!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dynamicModal')).hide();
                    break;

                case 'addLeave':
                    showAlert('تم إضافة الإجازة بنجاح!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dynamicModal')).hide();
                    break;

                case 'addDelegation':
                    showAlert('تم إضافة الإيفاد بنجاح!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dynamicModal')).hide();
                    break;
            }
        }

        function validateEmployeeData(data) {
            for (let key in data) {
                if (!data[key]) {
                    showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                    return false;
                }
            }
            return true;
        }

        function showAlert(message, type = 'success') {
            const alertTypes = {
                success: 'alert-success',
                warning: 'alert-warning',
                info: 'alert-info',
                danger: 'alert-danger'
            };

            const icons = {
                success: 'bi-check-circle-fill',
                warning: 'bi-exclamation-triangle-fill',
                info: 'bi-info-circle-fill',
                danger: 'bi-x-circle-fill'
            };

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertTypes[type]} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="bi ${icons[type]} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(alertDiv);

            // إزالة التنبيه بعد 3 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
