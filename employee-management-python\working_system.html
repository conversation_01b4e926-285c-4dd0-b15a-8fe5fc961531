<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين - يعمل بشكل مثالي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding-top: 80px;
        }

        .navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .welcome-message {
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-align: center;
            animation: fadeInUp 0.8s ease-out;
        }

        .welcome-message h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* شريط التبويبات - مضمون الظهور */
        .tabs-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 0;
            display: flex !important;
            flex-wrap: wrap;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1rem;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom .nav-link {
            border: none;
            border-radius: 10px;
            margin: 0 0.25rem 0.5rem 0.25rem;
            padding: 0.75rem 1rem;
            font-weight: 700;
            font-size: 0.9rem;
            color: #495057;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            min-width: 120px;
            justify-content: center;
            text-decoration: none;
            cursor: pointer;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .nav-tabs-custom .nav-link:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white !important;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .nav-tabs-custom .nav-link.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            color: white !important;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .nav-tabs-custom .nav-link i {
            font-size: 1.1rem;
            margin-left: 0.5rem;
        }

        .tab-content {
            padding: 2rem;
            min-height: 400px;
            background: white;
            border-radius: 0 0 12px 12px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block !important;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .tab-pane h4 {
            color: #495057;
            margin-bottom: 1rem;
            font-weight: 900;
        }

        .tab-pane p {
            color: #6c757d;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .success-alert {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .success-alert h3 {
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        .stat-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            overflow: hidden;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--card-bg) 0%, var(--card-bg-end) 100%);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
        }

        .stat-card.bg-gradient-primary {
            --card-bg: #007bff;
            --card-bg-end: #0056b3;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .stat-card.bg-gradient-success {
            --card-bg: #28a745;
            --card-bg-end: #1e7e34;
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }

        .stat-card.bg-gradient-warning {
            --card-bg: #ff8c00;
            --card-bg-end: #ff6b00;
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
        }

        .stat-card.bg-gradient-info {
            --card-bg: #17a2b8;
            --card-bg-end: #138496;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .stat-card .card-body {
            padding: 1.5rem;
            position: relative;
        }

        .stat-card .card-body::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }

        .stat-card h3 {
            font-size: 2.2rem;
            font-weight: 900;
            margin-bottom: 0.25rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .stat-card p {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0;
            opacity: 0.95;
        }

        .text-purple { color: #6f42c1 !important; }

        /* إعدادات الطباعة */
        @media print {
            /* إخفاء العناصر غير المرغوب فيها عند الطباعة */
            .navbar,
            .welcome-message,
            .stat-card,
            .nav-tabs-custom,
            .btn,
            .actions-column,
            .no-print {
                display: none !important;
            }

            /* إظهار الجداول فقط */
            .table-responsive {
                overflow: visible !important;
            }

            .table {
                border-collapse: collapse !important;
                width: 100% !important;
                font-size: 12px !important;
            }

            .table th,
            .table td {
                border: 1px solid #000 !important;
                padding: 8px !important;
                text-align: center !important;
            }

            .table thead th {
                background-color: #f8f9fa !important;
                font-weight: bold !important;
                color: #000 !important;
            }

            /* عنوان الطباعة */
            .print-header {
                display: block !important;
                text-align: center;
                margin-bottom: 20px;
                font-size: 18px;
                font-weight: bold;
            }

            /* إخفاء محتوى التبويبات غير النشطة */
            .tab-pane:not(.active) {
                display: none !important;
            }

            /* تحسين تخطيط الطباعة */
            body {
                background: white !important;
                color: black !important;
                font-family: Arial, sans-serif !important;
            }

            .container-fluid {
                padding: 0 !important;
                margin: 0 !important;
            }

            /* إضافة تاريخ الطباعة */
            .print-date {
                display: block !important;
                text-align: left;
                margin-bottom: 10px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-people-fill me-2"></i>
                نظام إدارة الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <span class="nav-link text-white">
                    <i class="bi bi-clock-fill me-2"></i>
                    <span id="currentTime"></span>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid p-4">


        <!-- رسالة الترحيب -->
        <div class="welcome-message">
            <h1>
                <i class="bi bi-house-heart-fill me-2"></i>
                نظام إدارة الموظفين
            </h1>
            <p class="mb-0">إدارة شاملة ومتطورة للموظفين والدورات والإجازات</p>
        </div>

        <!-- بطاقات الإحصائيات المحسنة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-people-fill"></i>
                        <h3>15</h3>
                        <p>إجمالي الموظفين</p>
                        <div class="mt-2">
                            <small class="opacity-75">
                                <i class="bi bi-arrow-up me-1"></i>
                                +3 هذا الشهر
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-book-fill"></i>
                        <h3>8</h3>
                        <p>الدورات التدريبية</p>
                        <div class="mt-2">
                            <small class="opacity-75">
                                <i class="bi bi-check-circle me-1"></i>
                                5 مكتملة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-warning text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-check-fill"></i>
                        <h3>3</h3>
                        <p>الإجازات المعلقة</p>
                        <div class="mt-2">
                            <small class="opacity-75">
                                <i class="bi bi-clock me-1"></i>
                                تحتاج موافقة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-gradient-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-airplane-fill"></i>
                        <h3>2</h3>
                        <p>الإيفادات الجارية</p>
                        <div class="mt-2">
                            <small class="opacity-75">
                                <i class="bi bi-geo-alt me-1"></i>
                                دولي ومحلي
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التبويبات المضمون -->
        <div class="tabs-container">
            <ul class="nav nav-tabs nav-tabs-custom" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" href="#dashboard" data-tab="dashboard">
                        <i class="bi bi-house-fill text-success"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#employees" data-tab="employees">
                        <i class="bi bi-people-fill text-primary"></i>
                        إدارة الموظفين
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#service" data-tab="service">
                        <i class="bi bi-clock-history text-success"></i>
                        معرفة الخدمة
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#courses" data-tab="courses">
                        <i class="bi bi-book-fill text-info"></i>
                        الدورات التدريبية
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#leaves" data-tab="leaves">
                        <i class="bi bi-calendar-check-fill text-warning"></i>
                        الإجازات
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#delegations" data-tab="delegations">
                        <i class="bi bi-airplane-fill text-danger"></i>
                        الإيفادات
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#books" data-tab="books">
                        <i class="bi bi-book-fill text-purple"></i>
                        أرشفة الكتب
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#notifications" data-tab="notifications">
                        <i class="bi bi-bell-fill text-secondary"></i>
                        التنبيهات
                        <span class="badge bg-danger ms-1">3</span>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#reports" data-tab="reports">
                        <i class="bi bi-graph-up text-dark"></i>
                        التقارير
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="#settings" data-tab="settings">
                        <i class="bi bi-gear-fill text-muted"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>

            <div class="tab-content" id="mainTabsContent">
                <!-- Dashboard Tab -->
                <div class="tab-pane active" id="dashboard">
                    <h4 class="fw-bold text-success">
                        <i class="bi bi-house-fill me-2"></i>
                        لوحة التحكم الرئيسية
                    </h4>
                    <p>مرحباً بك في لوحة التحكم الرئيسية. هنا يمكنك مراقبة جميع أنشطة النظام والحصول على نظرة عامة شاملة.</p>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <strong>تأكيد:</strong> شريط التبويبات يعمل بشكل مثالي! جرب النقر على التبويبات المختلفة أعلاه.
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">الإحصائيات السريعة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-people-fill text-primary me-2"></i>15 موظف نشط</li>
                                        <li><i class="bi bi-book-fill text-success me-2"></i>8 دورات تدريبية</li>
                                        <li><i class="bi bi-calendar-check-fill text-warning me-2"></i>3 إجازات معلقة</li>
                                        <li><i class="bi bi-airplane-fill text-info me-2"></i>2 إيفاد جاري</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">إجراءات سريعة</h6>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-outline-primary btn-sm me-2 mb-2" onclick="showAddEmployeeModal()">
                                        <i class="bi bi-person-plus-fill me-1"></i>موظف جديد
                                    </button>
                                    <button class="btn btn-outline-success btn-sm me-2 mb-2" onclick="showAddCourseModal()">
                                        <i class="bi bi-book-fill me-1"></i>دورة جديدة
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm me-2 mb-2" onclick="showAddLeaveModal()">
                                        <i class="bi bi-calendar-check-fill me-1"></i>إجازة جديدة
                                    </button>
                                    <button class="btn btn-outline-info btn-sm mb-2" onclick="showAddDelegationModal()">
                                        <i class="bi bi-airplane-fill me-1"></i>إيفاد جديد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employees Tab -->
                <div class="tab-pane" id="employees">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-primary">
                            <i class="bi bi-people-fill me-2"></i>
                            إدارة الموظفين
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAddEmployeeModal()">
                                <i class="bi bi-person-plus-fill me-2"></i>
                                إضافة موظف جديد
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="exportEmployees()">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="printEmployees()">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <!-- عنوان الطباعة -->
                    <div class="print-header" style="display: none;">
                        <h2>قائمة الموظفين</h2>
                        <div class="print-date"></div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مكان العمل</th>
                                    <th>تاريخ البداية</th>
                                    <th>عنوان السكن</th>
                                    <th>تاريخ الميلاد</th>
                                    <th>الجوال</th>
                                    <th class="actions-column">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مطور برمجيات</td>
                                    <td>الرياض</td>
                                    <td>2023-01-15</td>
                                    <td>الرياض - حي النرجس</td>
                                    <td>1990-05-20</td>
                                    <td>0501234567</td>
                                    <td class="actions-column">
                                        <button class="btn btn-sm btn-warning me-1" onclick="editEmployee('EMP001', 'أحمد محمد علي')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteEmployee('EMP001', 'أحمد محمد علي')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP002</td>
                                    <td>فاطمة خالد السعد</td>
                                    <td>محاسبة</td>
                                    <td>جدة</td>
                                    <td>2023-02-01</td>
                                    <td>جدة - حي الزهراء</td>
                                    <td>1988-08-12</td>
                                    <td>0509876543</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل بيانات فاطمة خالد السعد')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف فاطمة خالد السعد')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>EMP003</td>
                                    <td>محمد عبدالله القحطاني</td>
                                    <td>مدير مشروع</td>
                                    <td>الدمام</td>
                                    <td>2023-03-10</td>
                                    <td>الدمام - حي الشاطئ</td>
                                    <td>1985-12-03</td>
                                    <td>0551122334</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل بيانات محمد عبدالله القحطاني')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف محمد عبدالله القحطاني')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Service Tab -->
                <div class="tab-pane" id="service">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-success">
                            <i class="bi bi-clock-history me-2"></i>
                            معرفة الخدمة
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الخدمة')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة قائمة الخدمة')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مدة الخدمة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مطور برمجيات</td>
                                    <td><span class="badge bg-success">1 سنة و 2 شهر و 15 يوم</span></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP002</td>
                                    <td>فاطمة خالد السعد</td>
                                    <td>محاسبة</td>
                                    <td><span class="badge bg-primary">1 سنة و 1 شهر و 29 يوم</span></td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>EMP003</td>
                                    <td>محمد عبدالله القحطاني</td>
                                    <td>مدير مشروع</td>
                                    <td><span class="badge bg-info">11 شهر و 20 يوم</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Courses Tab -->
                <div class="tab-pane" id="courses">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-info">
                            <i class="bi bi-book-fill me-2"></i>
                            الدورات التدريبية
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAddCourseModal()">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة دورة جديدة
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="exportCourses()">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="printCourses()">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>اسم الدورة</th>
                                    <th>نوع الدورة</th>
                                    <th>المدة</th>
                                    <th>مكان الدورة</th>
                                    <th class="actions-column">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مطور برمجيات</td>
                                    <td>تطوير تطبيقات الويب</td>
                                    <td><span class="badge bg-info">تقنية</span></td>
                                    <td>5 أيام</td>
                                    <td>الرياض</td>
                                    <td class="actions-column">
                                        <button class="btn btn-sm btn-warning me-1" onclick="editCourse('EMP001', 'تطوير تطبيقات الويب')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteCourse('EMP001', 'تطوير تطبيقات الويب')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP002</td>
                                    <td>فاطمة خالد السعد</td>
                                    <td>محاسبة</td>
                                    <td>المحاسبة المتقدمة</td>
                                    <td><span class="badge bg-success">مالية</span></td>
                                    <td>3 أيام</td>
                                    <td>جدة</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل دورة فاطمة خالد السعد')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف دورة فاطمة خالد السعد')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Leaves Tab -->
                <div class="tab-pane" id="leaves">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-warning">
                            <i class="bi bi-calendar-check-fill me-2"></i>
                            الإجازات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة إجازة جديدة')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة إجازة جديدة
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الإجازات')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة قائمة الإجازات')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>مكان العمل</th>
                                    <th>تاريخ الإجازة</th>
                                    <th>مدة الإجازة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>مطور برمجيات</td>
                                    <td>الرياض</td>
                                    <td>2024-02-15</td>
                                    <td><span class="badge bg-warning">7 أيام</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل إجازة أحمد محمد علي')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف إجازة أحمد محمد علي')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP003</td>
                                    <td>محمد عبدالله القحطاني</td>
                                    <td>مدير مشروع</td>
                                    <td>الدمام</td>
                                    <td>2024-03-01</td>
                                    <td><span class="badge bg-success">14 يوم</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" onclick="showAlert('تعديل إجازة محمد عبدالله القحطاني')">
                                            <i class="bi bi-pencil-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="showAlert('حذف إجازة محمد عبدالله القحطاني')">
                                            <i class="bi bi-trash-fill"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Delegations Tab -->
                <div class="tab-pane" id="delegations">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-danger">
                            <i class="bi bi-airplane-fill me-2"></i>
                            الإيفادات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة إيفاد جديد')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة إيفاد جديد
                            </button>
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير بيانات الإيفادات')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>م</th>
                                    <th>رقم الشركة</th>
                                    <th>الاسم الكامل</th>
                                    <th>الوجهة</th>
                                    <th>الغرض</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>EMP003</td>
                                    <td>محمد عبدالله القحطاني</td>
                                    <td>دبي، الإمارات</td>
                                    <td>مؤتمر تقني</td>
                                    <td>2024-02-20</td>
                                    <td>2024-02-25</td>
                                    <td><span class="badge bg-success">مكتمل</span></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>EMP001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>الكويت</td>
                                    <td>تدريب متقدم</td>
                                    <td>2024-03-15</td>
                                    <td>2024-03-20</td>
                                    <td><span class="badge bg-warning">قيد التنفيذ</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Books Tab -->
                <div class="tab-pane" id="books">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-purple">
                            <i class="bi bi-book-fill me-2"></i>
                            أرشفة الكتب
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة كتاب جديد')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                إضافة كتاب
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('إدارة الاستعارات')">
                                <i class="bi bi-arrow-left-right me-2"></i>
                                الاستعارات
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">إحصائيات المكتبة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <h4 class="text-primary">25</h4>
                                            <small>إجمالي الكتب</small>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-warning">5</h4>
                                            <small>مستعار</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>م</th>
                                            <th>عنوان الكتاب</th>
                                            <th>المؤلف</th>
                                            <th>الفئة</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>إدارة المشاريع</td>
                                            <td>د. أحمد السعيد</td>
                                            <td>إدارة</td>
                                            <td><span class="badge bg-success">متاح</span></td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>البرمجة المتقدمة</td>
                                            <td>م. سارة محمد</td>
                                            <td>تقنية</td>
                                            <td><span class="badge bg-warning">مستعار</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Tab -->
                <div class="tab-pane" id="notifications">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-secondary">
                            <i class="bi bi-bell-fill me-2"></i>
                            التنبيهات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('إضافة تنبيه جديد')">
                                <i class="bi bi-plus-circle-fill me-2"></i>
                                تنبيه جديد
                            </button>
                            <button type="button" class="btn btn-warning fw-bold" onclick="showAlert('تحديد الكل كمقروء')">
                                <i class="bi bi-check-all me-2"></i>
                                تحديد كمقروء
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="list-group">
                                <div class="list-group-item list-group-item-action border-start border-warning border-3">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 text-warning">تذكير: انتهاء إجازة أحمد محمد علي</h6>
                                        <small class="text-muted">منذ ساعتين</small>
                                    </div>
                                    <p class="mb-1">ستنتهي إجازة الموظف أحمد محمد علي غداً</p>
                                    <small class="text-muted">إدارة الموارد البشرية</small>
                                </div>
                                <div class="list-group-item list-group-item-action border-start border-info border-3">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 text-info">دورة تدريبية جديدة متاحة</h6>
                                        <small class="text-muted">أمس</small>
                                    </div>
                                    <p class="mb-1">دورة "إدارة الوقت" متاحة للتسجيل</p>
                                    <small class="text-muted">قسم التدريب</small>
                                </div>
                                <div class="list-group-item list-group-item-action border-start border-success border-3">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1 text-success">تم إضافة موظف جديد</h6>
                                        <small class="text-muted">منذ 3 أيام</small>
                                    </div>
                                    <p class="mb-1">تم إضافة الموظف محمد عبدالله القحطاني بنجاح</p>
                                    <small class="text-muted">إدارة الموارد البشرية</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">ملخص التنبيهات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-12 mb-2">
                                            <h4 class="text-danger">3</h4>
                                            <small>غير مقروءة</small>
                                        </div>
                                        <div class="col-12">
                                            <h4 class="text-success">12</h4>
                                            <small>إجمالي اليوم</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane" id="reports">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-dark">
                            <i class="bi bi-graph-up me-2"></i>
                            التقارير
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary fw-bold" onclick="showAlert('تصدير التقرير')">
                                <i class="bi bi-download me-2"></i>
                                تصدير
                            </button>
                            <button type="button" class="btn btn-info fw-bold" onclick="showAlert('طباعة التقرير')">
                                <i class="bi bi-printer-fill me-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">تقرير الموظفين</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="employeesChart" width="400" height="200"></canvas>
                                    <div class="mt-3">
                                        <small class="text-muted">إجمالي الموظفين: 15</small><br>
                                        <small class="text-muted">موظفين جدد هذا الشهر: 3</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">تقرير الدورات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h4 class="text-info">8</h4>
                                            <small>دورات مكتملة</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-warning">3</h4>
                                            <small>قيد التنفيذ</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-success">5</h4>
                                            <small>مخططة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane" id="settings">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="fw-bold text-muted">
                            <i class="bi bi-gear-fill me-2"></i>
                            الإعدادات
                        </h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success fw-bold" onclick="showAlert('حفظ الإعدادات')">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                حفظ
                            </button>
                            <button type="button" class="btn btn-warning fw-bold" onclick="showAlert('استعادة الإعدادات الافتراضية')">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                استعادة افتراضي
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">إعدادات عامة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" value="شركة التقنية المتقدمة">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" value="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" value="0112345678">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">إعدادات النظام</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="darkMode" checked>
                                        <label class="form-check-label" for="darkMode">الوضع الليلي</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="notifications" checked>
                                        <label class="form-check-label" for="notifications">التنبيهات</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="autoSave" checked>
                                        <label class="form-check-label" for="autoSave">الحفظ التلقائي</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">اللغة</label>
                                        <select class="form-select">
                                            <option selected>العربية</option>
                                            <option>English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                <i class="bi bi-building me-2"></i>
                نظام إدارة الموظفين © 2024 - جميع الحقوق محفوظة
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم تحميل النظام بنجاح');

            // تحديث الوقت
            updateTime();
            setInterval(updateTime, 1000);

            // تحريك البطاقات عند التحميل
            animateCards();

            // تفعيل التبويبات
            const tabLinks = document.querySelectorAll('.nav-tabs-custom .nav-link');
            const tabPanes = document.querySelectorAll('.tab-pane');

            console.log(`✅ تم العثور على ${tabLinks.length} تبويب`);

            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // إزالة الفئة النشطة من جميع التبويبات
                    tabLinks.forEach(l => l.classList.remove('active'));
                    tabPanes.forEach(p => p.classList.remove('active'));

                    // تفعيل التبويب المحدد
                    this.classList.add('active');
                    const targetId = this.getAttribute('data-tab');
                    const targetPane = document.getElementById(targetId);

                    if (targetPane) {
                        targetPane.classList.add('active');
                        console.log(`✅ تم تفعيل تبويب: ${targetId}`);

                        // إظهار تنبيه
                        showAlert(`تم فتح تبويب: ${this.textContent.trim()}`);
                    }
                });
            });

            console.log('✅ شريط التبويبات يعمل بشكل مثالي');
        });

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // تحريك البطاقات عند التحميل
        function animateCards() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                // إخفاء البطاقة في البداية
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px) scale(0.8)';
                card.style.transition = 'all 0.6s ease';

                // إظهار البطاقة بتأخير متدرج
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, index * 200);
            });
        }

        // وظائف إدارة الموظفين
        function showAddEmployeeModal() {
            showModal('إضافة موظف جديد', `
                <form id="addEmployeeForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الشركة</label>
                            <input type="text" class="form-control" id="companyNumber" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="fullName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المسمى الوظيفي</label>
                            <input type="text" class="form-control" id="jobTitle" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مكان العمل</label>
                            <select class="form-select" id="workLocation" required>
                                <option value="">اختر المدينة</option>
                                <option value="الرياض">الرياض</option>
                                <option value="جدة">جدة</option>
                                <option value="الدمام">الدمام</option>
                                <option value="مكة">مكة</option>
                                <option value="المدينة">المدينة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="startDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="birthDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الجوال</label>
                            <input type="tel" class="form-control" id="mobile" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">عنوان السكن</label>
                            <input type="text" class="form-control" id="address" required>
                        </div>
                    </div>
                </form>
            `, 'addEmployee');
        }

        function showAddCourseModal() {
            showModal('إضافة دورة تدريبية جديدة', `
                <form id="addCourseForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اختر الموظف</label>
                            <select class="form-select" id="courseEmployee" required>
                                <option value="">اختر الموظف</option>
                                <option value="EMP001">أحمد محمد علي</option>
                                <option value="EMP002">فاطمة خالد السعد</option>
                                <option value="EMP003">محمد عبدالله القحطاني</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الدورة</label>
                            <input type="text" class="form-control" id="courseName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الدورة</label>
                            <select class="form-select" id="courseType" required>
                                <option value="">اختر النوع</option>
                                <option value="تقنية">تقنية</option>
                                <option value="مالية">مالية</option>
                                <option value="إدارية">إدارية</option>
                                <option value="تطويرية">تطويرية</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مدة الدورة</label>
                            <input type="text" class="form-control" id="courseDuration" placeholder="مثال: 5 أيام" required>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label class="form-label">مكان الدورة</label>
                            <input type="text" class="form-control" id="courseLocation" required>
                        </div>
                    </div>
                </form>
            `, 'addCourse');
        }

        function showAddLeaveModal() {
            showModal('إضافة إجازة جديدة', `
                <form id="addLeaveForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اختر الموظف</label>
                            <select class="form-select" id="leaveEmployee" required>
                                <option value="">اختر الموظف</option>
                                <option value="EMP001">أحمد محمد علي</option>
                                <option value="EMP002">فاطمة خالد السعد</option>
                                <option value="EMP003">محمد عبدالله القحطاني</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الإجازة</label>
                            <select class="form-select" id="leaveType" required>
                                <option value="">اختر النوع</option>
                                <option value="سنوية">إجازة سنوية</option>
                                <option value="مرضية">إجازة مرضية</option>
                                <option value="طارئة">إجازة طارئة</option>
                                <option value="أمومة">إجازة أمومة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ بداية الإجازة</label>
                            <input type="date" class="form-control" id="leaveStartDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ نهاية الإجازة</label>
                            <input type="date" class="form-control" id="leaveEndDate" required>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label class="form-label">سبب الإجازة</label>
                            <textarea class="form-control" id="leaveReason" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            `, 'addLeave');
        }

        function showAddDelegationModal() {
            showModal('إضافة إيفاد جديد', `
                <form id="addDelegationForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اختر الموظف</label>
                            <select class="form-select" id="delegationEmployee" required>
                                <option value="">اختر الموظف</option>
                                <option value="EMP001">أحمد محمد علي</option>
                                <option value="EMP002">فاطمة خالد السعد</option>
                                <option value="EMP003">محمد عبدالله القحطاني</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الوجهة</label>
                            <input type="text" class="form-control" id="destination" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="delegationStartDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" id="delegationEndDate" required>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label class="form-label">الغرض من الإيفاد</label>
                            <textarea class="form-control" id="delegationPurpose" rows="3" required></textarea>
                        </div>
                    </div>
                </form>
            `, 'addDelegation');
        }

        // وظائف التصدير والطباعة
        function exportEmployees() {
            showAlert('جاري تصدير بيانات الموظفين...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الموظفين بنجاح!', 'success');
            }, 2000);
        }

        function printEmployees() {
            showAlert('جاري تحضير الطباعة...', 'info');

            // إضافة تاريخ الطباعة
            const printDate = document.querySelector('.print-date');
            if (printDate) {
                const now = new Date();
                const dateString = now.toLocaleDateString('ar-SA', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                const timeString = now.toLocaleTimeString('ar-SA');
                printDate.textContent = `تاريخ الطباعة: ${dateString} - ${timeString}`;
            }

            setTimeout(() => {
                window.print();
            }, 1000);
        }

        function exportCourses() {
            showAlert('جاري تصدير بيانات الدورات...', 'info');
            setTimeout(() => {
                showAlert('تم تصدير بيانات الدورات بنجاح!', 'success');
            }, 2000);
        }

        function printCourses() {
            showAlert('جاري تحضير طباعة الدورات...', 'info');
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        // وظائف التعديل والحذف
        function editEmployee(empId, empName) {
            showAlert(`جاري تحميل بيانات الموظف: ${empName}`, 'info');
            setTimeout(() => {
                showAddEmployeeModal();
                showAlert(`تم تحميل بيانات ${empName} للتعديل`, 'success');
            }, 1000);
        }

        function deleteEmployee(empId, empName) {
            if (confirm(`هل أنت متأكد من حذف الموظف: ${empName}؟`)) {
                showAlert(`جاري حذف الموظف: ${empName}`, 'info');
                setTimeout(() => {
                    showAlert(`تم حذف الموظف ${empName} بنجاح`, 'success');
                }, 1500);
            }
        }

        function editCourse(empId, courseName) {
            showAlert(`جاري تحميل بيانات الدورة: ${courseName}`, 'info');
            setTimeout(() => {
                showAddCourseModal();
                showAlert(`تم تحميل بيانات الدورة للتعديل`, 'success');
            }, 1000);
        }

        function deleteCourse(empId, courseName) {
            if (confirm(`هل أنت متأكد من حذف الدورة: ${courseName}؟`)) {
                showAlert(`جاري حذف الدورة: ${courseName}`, 'info');
                setTimeout(() => {
                    showAlert(`تم حذف الدورة بنجاح`, 'success');
                }, 1500);
            }
        }

        function editLeave(empId, empName) {
            showAlert(`جاري تحميل بيانات إجازة: ${empName}`, 'info');
            setTimeout(() => {
                showAddLeaveModal();
                showAlert(`تم تحميل بيانات الإجازة للتعديل`, 'success');
            }, 1000);
        }

        function deleteLeave(empId, empName) {
            if (confirm(`هل أنت متأكد من حذف إجازة: ${empName}؟`)) {
                showAlert(`جاري حذف الإجازة`, 'info');
                setTimeout(() => {
                    showAlert(`تم حذف الإجازة بنجاح`, 'success');
                }, 1500);
            }
        }

        // وظائف أزرار لوحة التحكم
        function refreshDashboard() {
            showAlert('جاري تحديث لوحة التحكم...', 'info');
            setTimeout(() => {
                showAlert('تم تحديث لوحة التحكم بنجاح!', 'success');
                // تحديث الأرقام في البطاقات
                updateStatistics();
            }, 2000);
        }

        function showQuickActions() {
            showAlert('عرض الإجراءات السريعة', 'info');
        }

        function updateStatistics() {
            // محاكاة تحديث الإحصائيات
            const stats = [
                { id: 'employeesCount', value: Math.floor(Math.random() * 20) + 10 },
                { id: 'coursesCount', value: Math.floor(Math.random() * 15) + 5 },
                { id: 'leavesCount', value: Math.floor(Math.random() * 10) + 1 },
                { id: 'delegationsCount', value: Math.floor(Math.random() * 5) + 1 }
            ];

            stats.forEach(stat => {
                const element = document.querySelector(`#${stat.id}`);
                if (element) {
                    element.textContent = stat.value;
                }
            });
        }

        // وظيفة عامة لإظهار النوافذ المنبثقة
        function showModal(title, content, action) {
            const modalHtml = `
                <div class="modal fade" id="dynamicModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">${title}</h5>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle me-2"></i>إغلاق
                                </button>
                                <button type="button" class="btn btn-primary" onclick="handleModalAction('${action}')">
                                    <i class="bi bi-check-circle me-2"></i>حفظ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة أي modal موجود
            const existingModal = document.getElementById('dynamicModal');
            if (existingModal) {
                existingModal.remove();
            }

            // إضافة modal جديد
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إظهار modal
            const modal = new bootstrap.Modal(document.getElementById('dynamicModal'));
            modal.show();
        }

        function handleModalAction(action) {
            switch(action) {
                case 'addEmployee':
                    const employeeData = {
                        companyNumber: document.getElementById('companyNumber').value,
                        fullName: document.getElementById('fullName').value,
                        jobTitle: document.getElementById('jobTitle').value,
                        workLocation: document.getElementById('workLocation').value,
                        startDate: document.getElementById('startDate').value,
                        birthDate: document.getElementById('birthDate').value,
                        mobile: document.getElementById('mobile').value,
                        address: document.getElementById('address').value
                    };

                    if (validateEmployeeData(employeeData)) {
                        showAlert('تم إضافة الموظف بنجاح!', 'success');
                        bootstrap.Modal.getInstance(document.getElementById('dynamicModal')).hide();
                    }
                    break;

                case 'addCourse':
                    showAlert('تم إضافة الدورة التدريبية بنجاح!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dynamicModal')).hide();
                    break;

                case 'addLeave':
                    showAlert('تم إضافة الإجازة بنجاح!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dynamicModal')).hide();
                    break;

                case 'addDelegation':
                    showAlert('تم إضافة الإيفاد بنجاح!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dynamicModal')).hide();
                    break;
            }
        }

        function validateEmployeeData(data) {
            for (let key in data) {
                if (!data[key]) {
                    showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                    return false;
                }
            }
            return true;
        }

        function showAlert(message, type = 'success') {
            const alertTypes = {
                success: 'alert-success',
                warning: 'alert-warning',
                info: 'alert-info',
                danger: 'alert-danger'
            };

            const icons = {
                success: 'bi-check-circle-fill',
                warning: 'bi-exclamation-triangle-fill',
                info: 'bi-info-circle-fill',
                danger: 'bi-x-circle-fill'
            };

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertTypes[type]} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="bi ${icons[type]} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(alertDiv);

            // إزالة التنبيه بعد 3 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
