#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إدارة الموظفين
ملف التشغيل الرئيسي
"""

import sys
import os
import webbrowser
import time
from threading import Timer

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def open_browser():
    """فتح المتصفح تلقائياً"""
    webbrowser.open('http://localhost:5000')

def main():
    """الوظيفة الرئيسية"""
    try:
        print("=" * 60)
        print("🚀 نظام إدارة الموظفين - الإصدار المحسن")
        print("=" * 60)
        print("📋 جاري تحميل النظام...")

        # استيراد التطبيق
        from app import app

        print("✅ تم تحميل التطبيق بنجاح")
        print("🌐 الخادم متاح على: http://localhost:5000")
        print("📱 سيتم فتح المتصفح تلقائياً خلال 3 ثوان...")
        print("=" * 60)
        print("🎯 المميزات الجديدة:")
        print("   ✅ رسالة الترحيب محسنة ومرئية")
        print("   ✅ بطاقات الإحصائيات محسنة")
        print("   ✅ جميع الأزرار مفعلة ووظيفية")
        print("   ✅ تصدير وطباعة احترافية")
        print("   ✅ إجراءات سريعة متقدمة")
        print("=" * 60)
        print("💡 للإيقاف: اضغط Ctrl+C")
        print("=" * 60)

        # فتح المتصفح بعد 3 ثوان
        Timer(3.0, open_browser).start()

        # تشغيل الخادم
        app.run(
            debug=False,  # تعطيل debug لتحسين الأداء
            host='0.0.0.0',
            port=5000,
            use_reloader=False,
            threaded=True  # تفعيل المعالجة المتوازية
        )

    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 تأكد من تثبيت Flask و flask-cors")
        print("📦 pip install flask flask-cors")
        input("اضغط Enter للمتابعة...")

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للمتابعة...")

    finally:
        print("\n👋 تم إيقاف النظام")

if __name__ == '__main__':
    main()
