#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إدارة الموظفين
ملف التشغيل الرئيسي
"""

import sys
import os
import webbrowser
import time
from threading import Timer

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def open_browser():
    """فتح المتصفح تلقائياً"""
    webbrowser.open('http://localhost:5000')

def main():
    """الوظيفة الرئيسية"""
    try:
        print("=" * 50)
        print("🚀 نظام إدارة الموظفين")
        print("=" * 50)
        print("📋 جاري تحميل النظام...")
        
        # استيراد التطبيق
        from app import app
        
        print("✅ تم تحميل التطبيق بنجاح")
        print("🌐 الخادم متاح على: http://localhost:5000")
        print("📱 سيتم فتح المتصفح تلقائياً خلال 3 ثوان...")
        print("=" * 50)
        print("💡 للإيقاف: اضغط Ctrl+C")
        print("=" * 50)
        
        # فتح المتصفح بعد 3 ثوان
        Timer(3.0, open_browser).start()
        
        # تشغيل الخادم
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # تعطيل إعادة التحميل لتجنب فتح المتصفح مرتين
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 تأكد من تثبيت Flask و flask-cors")
        print("📦 pip install flask flask-cors")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n👋 تم إيقاف النظام")

if __name__ == '__main__':
    main()
