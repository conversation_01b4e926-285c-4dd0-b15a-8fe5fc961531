<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض شريط التبويبات - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding-top: 80px;
        }

        .navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .welcome-message {
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        .welcome-message h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        /* شريط التبويبات المحسن */
        .tabs-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .nav-tabs-custom {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 0;
            display: flex !important;
            flex-wrap: wrap;
            background: #f8f9fa;
            padding: 0.5rem;
        }

        .nav-tabs-custom .nav-link {
            border: none;
            border-radius: 8px;
            margin: 0 0.25rem;
            padding: 0.75rem 1rem;
            font-weight: 600;
            font-size: 0.9rem;
            color: #6c757d;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center;
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-tabs-custom .nav-link:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }

        .nav-tabs-custom .nav-link.active {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }

        .nav-tabs-custom .nav-link i {
            font-size: 1rem;
            margin-left: 0.5rem;
        }

        .tab-content {
            padding: 2rem;
            min-height: 400px;
        }

        .tab-pane h4 {
            color: #495057;
            margin-bottom: 1rem;
        }

        .tab-pane p {
            color: #6c757d;
            font-size: 1.1rem;
        }

        .demo-alert {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .demo-alert h3 {
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        .text-purple { color: #6f42c1 !important; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-people-fill me-2"></i>
                نظام إدارة الموظفين - عرض شريط التبويبات
            </a>
            <div class="navbar-nav ms-auto">
                <span class="nav-link text-white">
                    <i class="bi bi-check-circle-fill me-2 text-success"></i>
                    شريط التبويبات يعمل بشكل مثالي
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid p-4">
        <!-- رسالة النجاح -->
        <div class="demo-alert">
            <h3>
                <i class="bi bi-check-circle-fill me-2"></i>
                تم إصلاح مشكلة شريط التبويبات بنجاح!
            </h3>
            <p class="mb-0">شريط التبويبات ظاهر الآن ويعمل بكامل وظائفه</p>
        </div>

        <!-- رسالة الترحيب -->
        <div class="welcome-message">
            <h1>
                <i class="bi bi-house-heart-fill me-2"></i>
                نظام إدارة الموظفين
            </h1>
            <p class="mb-0">شريط التبويبات محسن ومرئي بوضوح</p>
        </div>

        <!-- شريط التبويبات المحسن -->
        <div class="tabs-container">
            <ul class="nav nav-tabs nav-tabs-custom" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                        <i class="bi bi-house-fill text-success"></i>
                        الرئيسية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="employees-tab" data-bs-toggle="tab" data-bs-target="#employees" type="button" role="tab">
                        <i class="bi bi-people-fill text-primary"></i>
                        إدارة الموظفين
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="service-tab" data-bs-toggle="tab" data-bs-target="#service" type="button" role="tab">
                        <i class="bi bi-clock-history text-success"></i>
                        معرفة الخدمة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab">
                        <i class="bi bi-book-fill text-info"></i>
                        الدورات التدريبية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="leaves-tab" data-bs-toggle="tab" data-bs-target="#leaves" type="button" role="tab">
                        <i class="bi bi-calendar-check-fill text-warning"></i>
                        الإجازات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="delegations-tab" data-bs-toggle="tab" data-bs-target="#delegations" type="button" role="tab">
                        <i class="bi bi-airplane-fill text-danger"></i>
                        الإيفادات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="books-tab" data-bs-toggle="tab" data-bs-target="#books" type="button" role="tab">
                        <i class="bi bi-book-fill text-purple"></i>
                        أرشفة الكتب
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                        <i class="bi bi-bell-fill text-secondary"></i>
                        التنبيهات
                        <span class="badge bg-danger ms-1">3</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab">
                        <i class="bi bi-graph-up text-dark"></i>
                        التقارير
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                        <i class="bi bi-gear-fill text-muted"></i>
                        الإعدادات
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="mainTabsContent">
                <!-- Dashboard Tab -->
                <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                    <h4 class="fw-bold text-success">
                        <i class="bi bi-house-fill me-2"></i>
                        لوحة التحكم الرئيسية
                    </h4>
                    <p>مرحباً بك في لوحة التحكم الرئيسية. هنا يمكنك مراقبة جميع أنشطة النظام والحصول على نظرة عامة شاملة.</p>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        <strong>ملاحظة:</strong> شريط التبويبات يعمل الآن بشكل مثالي! جرب النقر على التبويبات المختلفة.
                    </div>
                </div>

                <!-- Employees Tab -->
                <div class="tab-pane fade" id="employees" role="tabpanel">
                    <h4 class="fw-bold text-primary">
                        <i class="bi bi-people-fill me-2"></i>
                        إدارة الموظفين
                    </h4>
                    <p>إدارة شاملة لبيانات الموظفين، إضافة موظفين جدد، تعديل البيانات، وإدارة الملفات الشخصية.</p>
                </div>

                <!-- Service Tab -->
                <div class="tab-pane fade" id="service" role="tabpanel">
                    <h4 class="fw-bold text-success">
                        <i class="bi bi-clock-history me-2"></i>
                        معرفة الخدمة
                    </h4>
                    <p>عرض تفصيلي لمدة خدمة كل موظف، حساب سنوات الخبرة، والترقيات المستحقة.</p>
                </div>

                <!-- Courses Tab -->
                <div class="tab-pane fade" id="courses" role="tabpanel">
                    <h4 class="fw-bold text-info">
                        <i class="bi bi-book-fill me-2"></i>
                        الدورات التدريبية
                    </h4>
                    <p>إدارة الدورات التدريبية والتطويرية، تسجيل الموظفين في الدورات، ومتابعة التقدم.</p>
                </div>

                <!-- Leaves Tab -->
                <div class="tab-pane fade" id="leaves" role="tabpanel">
                    <h4 class="fw-bold text-warning">
                        <i class="bi bi-calendar-check-fill me-2"></i>
                        الإجازات
                    </h4>
                    <p>إدارة طلبات الإجازات، الموافقة على الطلبات، ومتابعة رصيد الإجازات لكل موظف.</p>
                </div>

                <!-- Delegations Tab -->
                <div class="tab-pane fade" id="delegations" role="tabpanel">
                    <h4 class="fw-bold text-danger">
                        <i class="bi bi-airplane-fill me-2"></i>
                        الإيفادات
                    </h4>
                    <p>إدارة إيفادات الموظفين للمهام الخارجية، السفر، والمؤتمرات.</p>
                </div>

                <!-- Books Tab -->
                <div class="tab-pane fade" id="books" role="tabpanel">
                    <h4 class="fw-bold text-purple">
                        <i class="bi bi-book-fill me-2"></i>
                        أرشفة الكتب
                    </h4>
                    <p>إدارة مكتبة الشركة، نظام الاستعارة، وتتبع الكتب المتاحة والمستعارة.</p>
                </div>

                <!-- Notifications Tab -->
                <div class="tab-pane fade" id="notifications" role="tabpanel">
                    <h4 class="fw-bold text-secondary">
                        <i class="bi bi-bell-fill me-2"></i>
                        التنبيهات
                    </h4>
                    <p>إدارة التنبيهات والإشعارات، إرسال رسائل للموظفين، والتذكيرات المهمة.</p>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane fade" id="reports" role="tabpanel">
                    <h4 class="fw-bold text-dark">
                        <i class="bi bi-graph-up me-2"></i>
                        التقارير
                    </h4>
                    <p>تقارير شاملة وإحصائيات تفصيلية عن جميع أنشطة النظام والموظفين.</p>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings" role="tabpanel">
                    <h4 class="fw-bold text-muted">
                        <i class="bi bi-gear-fill me-2"></i>
                        الإعدادات
                    </h4>
                    <p>إعدادات النظام، التخصيصات، إدارة المستخدمين، والصلاحيات.</p>
                </div>
            </div>
        </div>

        <!-- تأكيد النجاح -->
        <div class="alert alert-success text-center">
            <h5><i class="bi bi-check-circle-fill me-2"></i>تم إصلاح مشكلة شريط التبويبات بنجاح!</h5>
            <p class="mb-2">✅ شريط التبويبات ظاهر ومرئي بوضوح</p>
            <p class="mb-2">✅ جميع التبويبات العشرة تعمل بشكل مثالي</p>
            <p class="mb-2">✅ التصميم محسن وجميل</p>
            <p class="mb-0">✅ التنقل بين التبويبات سلس وسريع</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم تحميل عرض التبويبات بنجاح');
            
            // تفعيل التبويبات
            const tabButtons = document.querySelectorAll('#mainTabs button[data-bs-toggle="tab"]');
            console.log(`✅ تم العثور على ${tabButtons.length} تبويب`);
            
            tabButtons.forEach((button, index) => {
                button.addEventListener('click', function() {
                    console.log(`✅ تم النقر على التبويب: ${this.textContent.trim()}`);
                });
            });
            
            console.log('✅ شريط التبويبات يعمل بشكل مثالي');
        });
    </script>
</body>
</html>
