#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشغيل مبسط لنظام إدارة الموظفين
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    print("🚀 نظام إدارة الموظفين")
    print("=" * 40)
    
    try:
        # استيراد Flask
        from flask import Flask, render_template, jsonify, request
        from flask_cors import CORS
        import sqlite3
        import json
        from datetime import datetime
        
        print("✅ تم تحميل المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = Flask(__name__)
        CORS(app)
        
        # إنشاء قاعدة البيانات
        def init_db():
            conn = sqlite3.connect('employees.db')
            cursor = conn.cursor()
            
            # جدول الموظفين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sequence INTEGER,
                    company_number TEXT,
                    full_name TEXT,
                    job_title TEXT,
                    work_location TEXT,
                    start_date TEXT,
                    birth_date TEXT,
                    address TEXT,
                    mobile TEXT
                )
            ''')
            
            # إدراج بيانات تجريبية
            cursor.execute('SELECT COUNT(*) FROM employees')
            if cursor.fetchone()[0] == 0:
                sample_employees = [
                    (1, 'EMP001', 'أحمد محمد علي', 'مطور برمجيات', 'الرياض', '2023-01-15', '1990-05-20', 'الرياض - حي النرجس', '0501234567'),
                    (2, 'EMP002', 'فاطمة خالد السعد', 'محاسبة', 'جدة', '2023-02-01', '1988-08-12', 'جدة - حي الزهراء', '0509876543'),
                    (3, 'EMP003', 'محمد عبدالله القحطاني', 'مدير مشروع', 'الدمام', '2023-03-10', '1985-12-03', 'الدمام - حي الشاطئ', '0551122334')
                ]
                
                cursor.executemany('''
                    INSERT INTO employees (sequence, company_number, full_name, job_title, work_location, start_date, birth_date, address, mobile)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', sample_employees)
            
            conn.commit()
            conn.close()
        
        # تهيئة قاعدة البيانات
        init_db()
        
        # الصفحة الرئيسية
        @app.route('/')
        def index():
            return render_template('index.html')
        
        # API للإحصائيات
        @app.route('/api/statistics')
        def get_statistics():
            conn = sqlite3.connect('employees.db')
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM employees')
            employees_count = cursor.fetchone()[0]
            
            conn.close()
            
            return jsonify({
                'employees_count': employees_count,
                'courses_count': 0,
                'leaves_count': 0,
                'delegations_count': 0,
                'books_count': 0,
                'borrowed_books': 0,
                'unread_notifications': 0
            })
        
        # API للموظفين
        @app.route('/api/employees')
        def get_employees():
            conn = sqlite3.connect('employees.db')
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM employees ORDER BY sequence')
            rows = cursor.fetchall()
            
            employees = []
            for row in rows:
                employees.append({
                    'id': row[0],
                    'sequence': row[1],
                    'company_number': row[2],
                    'full_name': row[3],
                    'job_title': row[4],
                    'work_location': row[5],
                    'start_date': row[6],
                    'birth_date': row[7],
                    'address': row[8],
                    'mobile': row[9]
                })
            
            conn.close()
            return jsonify(employees)
        
        # APIs أخرى (فارغة للآن)
        @app.route('/api/courses')
        def get_courses():
            return jsonify([])
        
        @app.route('/api/leaves')
        def get_leaves():
            return jsonify([])
        
        @app.route('/api/delegations')
        def get_delegations():
            return jsonify([])
        
        @app.route('/api/books')
        def get_books():
            return jsonify([])
        
        @app.route('/api/notifications')
        def get_notifications():
            return jsonify([])
        
        @app.route('/api/service')
        def get_service():
            return jsonify([])
        
        print("✅ تم إعداد التطبيق بنجاح")
        print("🌐 الخادم متاح على: http://localhost:5000")
        print("💡 للإيقاف: اضغط Ctrl+C")
        print("=" * 40)
        
        # تشغيل الخادم
        app.run(debug=True, host='127.0.0.1', port=5000)
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 قم بتثبيت المكتبات المطلوبة:")
        print("pip install flask flask-cors")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
