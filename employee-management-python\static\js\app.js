// نظام إدارة الموظفين - الوظائف التفاعلية

// المتغيرات العامة
let currentEmployeeId = null;
let currentCourseId = null;
let currentLeaveId = null;
let currentDelegationId = null;
let currentBookId = null;
let currentNotificationId = null;
let employees = [];
let settings = {};

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
function initializeApp() {
    loadSettings();
    loadDashboard(); // تحميل لوحة التحكم أولاً

    // تفعيل التبويبات
    const tabTriggerList = document.querySelectorAll('#mainTabs button[data-bs-toggle="tab"]');
    tabTriggerList.forEach(tabTrigger => {
        tabTrigger.addEventListener('shown.bs.tab', function(event) {
            const target = event.target.getAttribute('data-bs-target');
            handleTabChange(target);
        });
    });
}

// التعامل مع تغيير التبويبات
function handleTabChange(target) {
    switch(target) {
        case '#dashboard':
            loadDashboard();
            break;
        case '#employees':
            loadEmployees();
            break;
        case '#service':
            loadService();
            break;
        case '#courses':
            loadCourses();
            break;
        case '#leaves':
            loadLeaves();
            break;
        case '#delegations':
            loadDelegations();
            break;
        case '#books':
            loadBooks();
            break;
        case '#notifications':
            loadNotifications();
            break;
        case '#settings':
            loadSettings();
            break;
    }
}

// تحميل لوحة التحكم
async function loadDashboard() {
    await loadStatistics();
    loadRecentActivity();
}

// تحميل الإحصائيات
async function loadStatistics() {
    try {
        const response = await fetch('/api/statistics');
        const stats = await response.json();

        // تحديث البطاقات الرئيسية
        document.getElementById('employeesCount').textContent = stats.employees_count;
        document.getElementById('coursesCount').textContent = stats.courses_count;
        document.getElementById('leavesCount').textContent = stats.leaves_count;
        document.getElementById('delegationsCount').textContent = stats.delegations_count;

        // تحديث بطاقات لوحة التحكم
        const dashboardEmployeesCount = document.getElementById('dashboardEmployeesCount');
        const dashboardCoursesCount = document.getElementById('dashboardCoursesCount');
        const dashboardLeavesCount = document.getElementById('dashboardLeavesCount');
        const dashboardDelegationsCount = document.getElementById('dashboardDelegationsCount');

        if (dashboardEmployeesCount) dashboardEmployeesCount.textContent = stats.employees_count;
        if (dashboardCoursesCount) dashboardCoursesCount.textContent = stats.courses_count;
        if (dashboardLeavesCount) dashboardLeavesCount.textContent = stats.leaves_count;
        if (dashboardDelegationsCount) dashboardDelegationsCount.textContent = stats.delegations_count;

        // تحديث إحصائيات الكتب
        const totalBooksCount = document.getElementById('totalBooksCount');
        const borrowedBooksCount = document.getElementById('borrowedBooksCount');
        const availableBooksCount = document.getElementById('availableBooksCount');

        if (totalBooksCount) totalBooksCount.textContent = stats.books_count || 0;
        if (borrowedBooksCount) borrowedBooksCount.textContent = stats.borrowed_books || 0;
        if (availableBooksCount) availableBooksCount.textContent = (stats.books_count || 0) - (stats.borrowed_books || 0);

        // تحديث شارة التنبيهات
        const notificationsBadge = document.getElementById('notificationsBadge');
        if (stats.unread_notifications > 0) {
            notificationsBadge.textContent = stats.unread_notifications;
            notificationsBadge.style.display = 'inline';
        } else {
            notificationsBadge.style.display = 'none';
        }
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
        showNotification('خطأ في تحميل الإحصائيات', 'error');
    }
}

// تحميل النشاط الأخير
function loadRecentActivity() {
    const recentActivityContainer = document.getElementById('recentActivity');
    if (!recentActivityContainer) return;

    // محاكاة النشاط الأخير
    const activities = [
        {
            icon: 'bi-person-plus-fill',
            color: 'primary',
            title: 'إضافة موظف جديد',
            description: 'تم إضافة الموظف أحمد محمد علي',
            time: 'منذ ساعتين'
        },
        {
            icon: 'bi-book-fill',
            color: 'success',
            title: 'دورة تدريبية جديدة',
            description: 'تم تسجيل دورة البرمجة المتقدمة',
            time: 'منذ 4 ساعات'
        },
        {
            icon: 'bi-calendar-check-fill',
            color: 'warning',
            title: 'إجازة معتمدة',
            description: 'تم اعتماد إجازة سارة خالد العتيبي',
            time: 'منذ يوم واحد'
        },
        {
            icon: 'bi-airplane-fill',
            color: 'info',
            title: 'إيفاد جديد',
            description: 'تم تسجيل إيفاد إلى دبي',
            time: 'منذ يومين'
        }
    ];

    let activityHTML = '';
    activities.forEach((activity, index) => {
        activityHTML += `
            <div class="d-flex align-items-start mb-2 ${index === activities.length - 1 ? '' : 'border-bottom pb-2'}">
                <div class="flex-shrink-0">
                    <div class="bg-${activity.color} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 0.75rem;">
                        <i class="bi ${activity.icon}"></i>
                    </div>
                </div>
                <div class="flex-grow-1 ms-2">
                    <h6 class="mb-0 fw-bold" style="font-size: 0.85rem;">${activity.title}</h6>
                    <p class="mb-0 text-muted" style="font-size: 0.75rem;">${activity.description}</p>
                    <small class="text-muted" style="font-size: 0.7rem;">
                        <i class="bi bi-clock me-1"></i>
                        ${activity.time}
                    </small>
                </div>
            </div>
        `;
    });

    recentActivityContainer.innerHTML = activityHTML;
}

// تحديث لوحة التحكم
function refreshDashboard() {
    showNotification('جاري تحديث البيانات...', 'info');
    loadDashboard();
    setTimeout(() => {
        showNotification('تم تحديث البيانات بنجاح', 'success');
    }, 1000);
}

// عرض الإجراءات السريعة
function showQuickActions() {
    const quickActionsModal = `
        <div class="modal fade" id="quickActionsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title fw-bold">
                            <i class="bi bi-lightning-fill me-2"></i>
                            الإجراءات السريعة
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-6">
                                <button class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="showAddEmployeeModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-person-plus-fill fs-1 mb-2"></i>
                                    <span class="fw-bold">إضافة موظف</span>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="showAddCourseModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-book-fill fs-1 mb-2"></i>
                                    <span class="fw-bold">إضافة دورة</span>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="showAddLeaveModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-calendar-check-fill fs-1 mb-2"></i>
                                    <span class="fw-bold">إضافة إجازة</span>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="showAddDelegationModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-airplane-fill fs-1 mb-2"></i>
                                    <span class="fw-bold">إضافة إيفاد</span>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="showAddNotificationModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-bell-fill fs-1 mb-2"></i>
                                    <span class="fw-bold">إضافة تنبيه</span>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="document.getElementById('reports-tab').click(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-graph-up fs-1 mb-2"></i>
                                    <span class="fw-bold">عرض التقارير</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة النموذج السابق إن وجد
    const existingModal = document.getElementById('quickActionsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النموذج الجديد
    document.body.insertAdjacentHTML('beforeend', quickActionsModal);

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('quickActionsModal'));
    modal.show();
}

// تحميل الموظفين
async function loadEmployees() {
    try {
        const response = await fetch('/api/employees');
        employees = await response.json();

        const tbody = document.getElementById('employeesTableBody');
        tbody.innerHTML = '';

        employees.forEach(employee => {
            const row = createEmployeeRow(employee);
            tbody.appendChild(row);
        });

        // تحديث قوائم الموظفين في النماذج
        updateEmployeeSelects();
    } catch (error) {
        console.error('خطأ في تحميل الموظفين:', error);
        showNotification('خطأ في تحميل بيانات الموظفين', 'error');
    }
}

// إنشاء صف موظف
function createEmployeeRow(employee) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${employee.sequence}</td>
        <td>${employee.company_number}</td>
        <td>${employee.full_name}</td>
        <td>${employee.job_title}</td>
        <td>${employee.work_location}</td>
        <td>${formatDate(employee.start_date)}</td>
        <td>${employee.address || '-'}</td>
        <td>${formatDate(employee.birth_date) || '-'}</td>
        <td>${employee.mobile || '-'}</td>
        <td>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="editEmployee(${employee.id})" title="تعديل">
                    <i class="bi bi-pencil-fill"></i>
                </button>
                <button class="btn btn-outline-danger" onclick="deleteEmployee(${employee.id})" title="حذف">
                    <i class="bi bi-trash-fill"></i>
                </button>
            </div>
        </td>
    `;
    return row;
}

// تحديث قوائم الموظفين في النماذج
function updateEmployeeSelects() {
    const selects = [
        'courseEmployeeId',
        'leaveEmployeeId',
        'delegationEmployeeId',
        'notificationEmployeeId'
    ];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // الاحتفاظ بالخيار الأول
            const firstOption = select.querySelector('option[value=""]');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.company_number} - ${employee.full_name}`;
                select.appendChild(option);
            });
        }
    });
}

// عرض نموذج إضافة موظف
function showAddEmployeeModal() {
    currentEmployeeId = null;
    document.getElementById('employeeModalTitle').textContent = 'إضافة موظف جديد';
    document.getElementById('employeeForm').reset();
    document.getElementById('employeeId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
    modal.show();
}

// تعديل موظف
function editEmployee(id) {
    const employee = employees.find(emp => emp.id === id);
    if (!employee) return;

    currentEmployeeId = id;
    document.getElementById('employeeModalTitle').textContent = 'تعديل بيانات الموظف';

    // ملء النموذج
    document.getElementById('employeeId').value = employee.id;
    document.getElementById('companyNumber').value = employee.company_number;
    document.getElementById('fullName').value = employee.full_name;
    document.getElementById('jobTitle').value = employee.job_title;
    document.getElementById('workLocation').value = employee.work_location;
    document.getElementById('startDate').value = employee.start_date;
    document.getElementById('birthDate').value = employee.birth_date || '';
    document.getElementById('address').value = employee.address || '';
    document.getElementById('mobile').value = employee.mobile || '';

    const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
    modal.show();
}

// حفظ الموظف
async function saveEmployee() {
    const form = document.getElementById('employeeForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const employeeData = {
        company_number: document.getElementById('companyNumber').value,
        full_name: document.getElementById('fullName').value,
        job_title: document.getElementById('jobTitle').value,
        work_location: document.getElementById('workLocation').value,
        start_date: document.getElementById('startDate').value,
        birth_date: document.getElementById('birthDate').value,
        address: document.getElementById('address').value,
        mobile: document.getElementById('mobile').value
    };

    try {
        let response;
        if (currentEmployeeId) {
            // تحديث
            response = await fetch(`/api/employees/${currentEmployeeId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(employeeData)
            });
        } else {
            // إضافة جديد
            response = await fetch('/api/employees', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(employeeData)
            });
        }

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('employeeModal')).hide();
            loadEmployees();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الموظف:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// حذف موظف
async function deleteEmployee(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الموظف؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        return;
    }

    try {
        const response = await fetch(`/api/employees/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadEmployees();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الموظف', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الموظف:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// تحميل بيانات الخدمة
async function loadService() {
    try {
        const response = await fetch('/api/service');
        const serviceData = await response.json();

        const tbody = document.getElementById('serviceTableBody');
        tbody.innerHTML = '';

        serviceData.forEach(service => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${service.sequence}</td>
                <td>${service.company_number}</td>
                <td>${service.full_name}</td>
                <td>${service.job_title}</td>
                <td class="fw-bold text-primary">${service.service_duration}</td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل بيانات الخدمة:', error);
        showNotification('خطأ في تحميل بيانات الخدمة', 'error');
    }
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// عرض التنبيهات
function showNotification(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';

    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}-fill me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تبديل الثيم
function toggleTheme() {
    const body = document.body;
    const currentTheme = body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);

    showNotification(`تم تغيير الثيم إلى ${newTheme === 'dark' ? 'الداكن' : 'الفاتح'}`, 'success');
}

// تحميل الثيم المحفوظ
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.body.setAttribute('data-theme', savedTheme);
}

// تحميل الثيم عند بدء التطبيق
loadSavedTheme();

// تحميل الدورات
async function loadCourses() {
    try {
        const response = await fetch('/api/courses');
        const courses = await response.json();

        const tbody = document.getElementById('coursesTableBody');
        tbody.innerHTML = '';

        courses.forEach(course => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${course.sequence}</td>
                <td>${course.company_number}</td>
                <td>${course.full_name}</td>
                <td>${course.job_title}</td>
                <td>${course.title}</td>
                <td>${course.type}</td>
                <td>${course.period}</td>
                <td>${course.location}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="editCourse(${course.id})" title="تعديل">
                            <i class="bi bi-pencil-fill"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteCourse(${course.id})" title="حذف">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل الدورات:', error);
        showNotification('خطأ في تحميل بيانات الدورات', 'error');
    }
}

// تحميل الإجازات
async function loadLeaves() {
    try {
        const response = await fetch('/api/leaves');
        const leaves = await response.json();

        const tbody = document.getElementById('leavesTableBody');
        tbody.innerHTML = '';

        leaves.forEach(leave => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${leave.sequence}</td>
                <td>${leave.company_number}</td>
                <td>${leave.full_name}</td>
                <td>${leave.job_title}</td>
                <td>${leave.work_location}</td>
                <td>${formatDate(leave.leave_date)}</td>
                <td>${leave.leave_duration}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-warning" onclick="editLeave(${leave.id})" title="تعديل">
                            <i class="bi bi-pencil-fill"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteLeave(${leave.id})" title="حذف">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل الإجازات:', error);
        showNotification('خطأ في تحميل بيانات الإجازات', 'error');
    }
}

// تحميل الإيفادات
async function loadDelegations() {
    try {
        const response = await fetch('/api/delegations');
        const delegations = await response.json();

        const tbody = document.getElementById('delegationsTableBody');
        tbody.innerHTML = '';

        delegations.forEach(delegation => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${delegation.sequence}</td>
                <td>${delegation.company_number}</td>
                <td>${delegation.full_name}</td>
                <td>${delegation.job_title}</td>
                <td>${delegation.destination}</td>
                <td>${delegation.country}</td>
                <td>${delegation.purpose}</td>
                <td>${formatDate(delegation.start_date)}</td>
                <td>${formatDate(delegation.end_date)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-danger" onclick="deleteDelegation(${delegation.id})" title="حذف">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل الإيفادات:', error);
        showNotification('خطأ في تحميل بيانات الإيفادات', 'error');
    }
}

// تحميل التنبيهات
async function loadNotifications() {
    try {
        const response = await fetch('/api/notifications');
        const notifications = await response.json();

        const tbody = document.getElementById('notificationsTableBody');
        tbody.innerHTML = '';

        notifications.forEach(notification => {
            const row = document.createElement('tr');
            const isRead = notification.read === 1;

            row.className = isRead ? '' : 'table-warning';
            row.innerHTML = `
                <td>${notification.sequence}</td>
                <td>${notification.title}</td>
                <td>${notification.message}</td>
                <td><span class="badge bg-secondary">${notification.type}</span></td>
                <td>${formatDate(notification.date)}</td>
                <td>${notification.employee_name || '-'}</td>
                <td>
                    <span class="badge ${isRead ? 'bg-success' : 'bg-warning'}">
                        ${isRead ? 'مقروء' : 'غير مقروء'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        ${!isRead ? `<button class="btn btn-outline-success" onclick="markNotificationRead(${notification.id})" title="تحديد كمقروء">
                            <i class="bi bi-check-circle-fill"></i>
                        </button>` : ''}
                        <button class="btn btn-outline-danger" onclick="deleteNotification(${notification.id})" title="حذف">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل التنبيهات:', error);
        showNotification('خطأ في تحميل التنبيهات', 'error');
    }
}

// تحميل الإعدادات
async function loadSettings() {
    try {
        const response = await fetch('/api/settings');
        settings = await response.json();

        // تطبيق الإعدادات على الواجهة
        if (settings.theme) {
            document.body.setAttribute('data-theme', settings.theme);
            document.getElementById('themeSelect').value = settings.theme;
        }

        if (settings.font) {
            document.body.className = `font-${settings.font} ${document.body.className}`;
            document.getElementById('fontSelect').value = settings.font;
        }

        if (settings.font_size) {
            document.body.className = `font-size-${settings.font_size} ${document.body.className}`;
            document.getElementById('fontSizeSelect').value = settings.font_size;
        }

        if (settings.theme_color) {
            document.getElementById('themeColorSelect').value = settings.theme_color;
        }

        // إعدادات التنبيهات
        document.getElementById('notificationsEnabled').checked = settings.notifications_enabled === 1;
        document.getElementById('notificationDuration').value = settings.notification_duration / 1000;
        document.getElementById('employeeNotifications').checked = settings.employee_notifications === 1;
        document.getElementById('leaveNotifications').checked = settings.leave_notifications === 1;
        document.getElementById('courseNotifications').checked = settings.course_notifications === 1;

    } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
        showNotification('خطأ في تحميل الإعدادات', 'error');
    }
}

// تحميل الكتب
async function loadBooks() {
    try {
        const response = await fetch('/api/books');
        const books = await response.json();

        const tbody = document.getElementById('booksTableBody');
        tbody.innerHTML = '';

        books.forEach(book => {
            const row = createBookRow(book);
            tbody.appendChild(row);
        });

        // تحديث إحصائيات الكتب
        updateBooksStatistics(books);

    } catch (error) {
        console.error('خطأ في تحميل الكتب:', error);
        showNotification('خطأ في تحميل بيانات الكتب', 'error');
    }
}

// إنشاء صف كتاب
function createBookRow(book) {
    const row = document.createElement('tr');

    // تحديد لون الحالة
    let statusClass = 'success';
    let statusIcon = 'check-circle-fill';
    if (book.status === 'مُستعار') {
        statusClass = 'warning';
        statusIcon = 'arrow-repeat';
    } else if (book.status === 'غير متاح') {
        statusClass = 'danger';
        statusIcon = 'x-circle-fill';
    }

    row.innerHTML = `
        <td>${book.sequence}</td>
        <td class="fw-bold">${book.title}</td>
        <td>${book.author || '-'}</td>
        <td>
            ${book.category ? `<span class="badge bg-secondary">${book.category}</span>` : '-'}
        </td>
        <td>${book.publisher || '-'}</td>
        <td>${book.publication_year || '-'}</td>
        <td>${book.pages || '-'}</td>
        <td>${book.language || 'العربية'}</td>
        <td>${book.location || '-'}</td>
        <td>
            <span class="badge bg-${statusClass}">
                <i class="bi bi-${statusIcon} me-1"></i>
                ${book.status}
            </span>
            ${book.borrowed_by ? `<br><small class="text-muted">مُستعار من: ${book.borrowed_by}</small>` : ''}
        </td>
        <td>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="editBook(${book.id})" title="تعديل">
                    <i class="bi bi-pencil-fill"></i>
                </button>
                ${book.status === 'متاح' ?
                    `<button class="btn btn-outline-warning" onclick="showBorrowBookModal(${book.id}, '${book.title}')" title="استعارة">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>` :
                    book.status === 'مُستعار' ?
                    `<button class="btn btn-outline-success" onclick="returnBook(${book.id})" title="إرجاع">
                        <i class="bi bi-arrow-return-left"></i>
                    </button>` : ''
                }
                <button class="btn btn-outline-danger" onclick="deleteBook(${book.id})" title="حذف">
                    <i class="bi bi-trash-fill"></i>
                </button>
            </div>
        </td>
    `;
    return row;
}

// تحديث إحصائيات الكتب
function updateBooksStatistics(books) {
    const totalBooks = books.length;
    const borrowedBooks = books.filter(book => book.status === 'مُستعار').length;
    const availableBooks = books.filter(book => book.status === 'متاح').length;
    const categories = [...new Set(books.map(book => book.category).filter(cat => cat))];

    const totalBooksCount = document.getElementById('totalBooksCount');
    const borrowedBooksCount = document.getElementById('borrowedBooksCount');
    const availableBooksCount = document.getElementById('availableBooksCount');
    const categoriesCount = document.getElementById('categoriesCount');

    if (totalBooksCount) totalBooksCount.textContent = totalBooks;
    if (borrowedBooksCount) borrowedBooksCount.textContent = borrowedBooks;
    if (availableBooksCount) availableBooksCount.textContent = availableBooks;
    if (categoriesCount) categoriesCount.textContent = categories.length;
}

// عرض نموذج إضافة كتاب
function showAddBookModal() {
    currentBookId = null;
    document.getElementById('bookModalTitle').textContent = 'إضافة كتاب جديد';
    document.getElementById('bookForm').reset();
    document.getElementById('bookId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('bookModal'));
    modal.show();
}

// تعديل كتاب
function editBook(id) {
    // البحث عن الكتاب في البيانات المحملة
    fetch(`/api/books`)
        .then(response => response.json())
        .then(books => {
            const book = books.find(b => b.id === id);
            if (!book) return;

            currentBookId = id;
            document.getElementById('bookModalTitle').textContent = 'تعديل بيانات الكتاب';

            // ملء النموذج
            document.getElementById('bookId').value = book.id;
            document.getElementById('bookTitle').value = book.title;
            document.getElementById('bookAuthor').value = book.author || '';
            document.getElementById('bookIsbn').value = book.isbn || '';
            document.getElementById('bookCategory').value = book.category || '';
            document.getElementById('bookPublisher').value = book.publisher || '';
            document.getElementById('bookYear').value = book.publication_year || '';
            document.getElementById('bookPages').value = book.pages || '';
            document.getElementById('bookLanguage').value = book.language || 'العربية';
            document.getElementById('bookLocation').value = book.location || '';
            document.getElementById('bookStatus').value = book.status || 'متاح';
            document.getElementById('bookDescription').value = book.description || '';

            const modal = new bootstrap.Modal(document.getElementById('bookModal'));
            modal.show();
        })
        .catch(error => {
            console.error('خطأ في جلب بيانات الكتاب:', error);
            showNotification('خطأ في جلب بيانات الكتاب', 'error');
        });
}

// حفظ الكتاب
async function saveBook() {
    const form = document.getElementById('bookForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const bookData = {
        title: document.getElementById('bookTitle').value,
        author: document.getElementById('bookAuthor').value,
        isbn: document.getElementById('bookIsbn').value,
        category: document.getElementById('bookCategory').value,
        publisher: document.getElementById('bookPublisher').value,
        publication_year: document.getElementById('bookYear').value ? parseInt(document.getElementById('bookYear').value) : null,
        pages: document.getElementById('bookPages').value ? parseInt(document.getElementById('bookPages').value) : null,
        language: document.getElementById('bookLanguage').value,
        location: document.getElementById('bookLocation').value,
        status: document.getElementById('bookStatus').value,
        description: document.getElementById('bookDescription').value
    };

    try {
        let response;
        if (currentBookId) {
            // تحديث
            response = await fetch(`/api/books/${currentBookId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bookData)
            });
        } else {
            // إضافة جديد
            response = await fetch('/api/books', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bookData)
            });
        }

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('bookModal')).hide();
            loadBooks();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الكتاب:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// حذف كتاب
async function deleteBook(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الكتاب؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/books/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadBooks();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الكتاب', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الكتاب:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// عرض نموذج استعارة كتاب
function showBorrowBookModal(bookId, bookTitle) {
    document.getElementById('borrowBookId').value = bookId;
    document.getElementById('borrowBookTitle').value = bookTitle;
    document.getElementById('borrowerName').value = '';

    // تعيين تاريخ الإرجاع المتوقع (بعد أسبوعين)
    const returnDate = new Date();
    returnDate.setDate(returnDate.getDate() + 14);
    document.getElementById('returnDate').value = returnDate.toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('borrowBookModal'));
    modal.show();
}

// تأكيد استعارة الكتاب
async function confirmBorrowBook() {
    const form = document.getElementById('borrowBookForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const bookId = document.getElementById('borrowBookId').value;
    const borrowData = {
        borrowed_by: document.getElementById('borrowerName').value,
        return_date: document.getElementById('returnDate').value
    };

    try {
        const response = await fetch(`/api/books/${bookId}/borrow`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(borrowData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('borrowBookModal')).hide();
            loadBooks();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء استعارة الكتاب', 'error');
        }
    } catch (error) {
        console.error('خطأ في استعارة الكتاب:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// إرجاع كتاب
async function returnBook(id) {
    if (!confirm('هل أنت متأكد من إرجاع هذا الكتاب؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/books/${id}/return`, {
            method: 'PUT'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadBooks();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء إرجاع الكتاب', 'error');
        }
    } catch (error) {
        console.error('خطأ في إرجاع الكتاب:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// مسح فلاتر البحث
function clearBooksFilters() {
    document.getElementById('booksSearch').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('statusFilter').value = '';
    loadBooks();
}

// وظائف التصدير والطباعة للكتب
function exportBooks() {
    showNotification('جاري تصدير بيانات الكتب...', 'info');

    fetch('/api/books')
        .then(response => response.json())
        .then(data => {
            exportToCSV(data, 'books', 'بيانات_الكتب');
            showNotification('تم تصدير بيانات الكتب بنجاح', 'success');
        })
        .catch(error => {
            console.error('خطأ في تصدير الكتب:', error);
            showNotification('خطأ في تصدير البيانات', 'error');
        });
}

function printBooks() {
    showNotification('جاري تحضير الكتب للطباعة...', 'info');

    fetch('/api/books')
        .then(response => response.json())
        .then(data => {
            printTable(data, 'books', 'قائمة الكتب');
        })
        .catch(error => {
            console.error('خطأ في طباعة الكتب:', error);
            showNotification('خطأ في طباعة البيانات', 'error');
        });
}

// وظائف النماذج المنبثقة
function showAddCourseModal() {
    currentCourseId = null;
    document.getElementById('courseForm').reset();
    updateEmployeeSelects();

    const modal = new bootstrap.Modal(document.getElementById('courseModal'));
    modal.show();
}

function showAddLeaveModal() {
    currentLeaveId = null;
    document.getElementById('leaveForm').reset();
    updateEmployeeSelects();

    const modal = new bootstrap.Modal(document.getElementById('leaveModal'));
    modal.show();
}

function showAddDelegationModal() {
    currentDelegationId = null;
    document.getElementById('delegationForm').reset();
    updateEmployeeSelects();

    const modal = new bootstrap.Modal(document.getElementById('delegationModal'));
    modal.show();
}

function showAddNotificationModal() {
    document.getElementById('notificationForm').reset();
    updateEmployeeSelects();

    // تعيين التاريخ الحالي
    document.getElementById('notificationDate').value = new Date().toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
    modal.show();
}

// وظائف الحفظ
async function saveCourse() {
    const form = document.getElementById('courseForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const courseData = {
        employee_id: document.getElementById('courseEmployeeId').value,
        title: document.getElementById('courseTitle').value,
        type: document.getElementById('courseType').value,
        period: document.getElementById('coursePeriod').value,
        location: document.getElementById('courseLocation').value,
        start_date: document.getElementById('courseStartDate').value
    };

    try {
        const response = await fetch('/api/courses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(courseData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('courseModal')).hide();
            loadCourses();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الدورة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function saveLeave() {
    const form = document.getElementById('leaveForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const leaveData = {
        employee_id: document.getElementById('leaveEmployeeId').value,
        leave_type: document.getElementById('leaveType').value,
        leave_date: document.getElementById('leaveDate').value,
        leave_duration: document.getElementById('leaveDuration').value,
        leave_reason: document.getElementById('leaveReason').value
    };

    try {
        const response = await fetch('/api/leaves', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(leaveData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('leaveModal')).hide();
            loadLeaves();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الإجازة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function saveDelegation() {
    const form = document.getElementById('delegationForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const delegationData = {
        employee_id: document.getElementById('delegationEmployeeId').value,
        destination: document.getElementById('delegationDestination').value,
        country: document.getElementById('delegationCountry').value,
        purpose: document.getElementById('delegationPurpose').value,
        start_date: document.getElementById('delegationStartDate').value,
        end_date: document.getElementById('delegationEndDate').value
    };

    try {
        const response = await fetch('/api/delegations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(delegationData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('delegationModal')).hide();
            loadDelegations();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الإيفاد:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function saveNotification() {
    const form = document.getElementById('notificationForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const notificationData = {
        title: document.getElementById('notificationTitle').value,
        message: document.getElementById('notificationMessage').value,
        type: document.getElementById('notificationType').value,
        date: document.getElementById('notificationDate').value,
        employee_id: document.getElementById('notificationEmployeeId').value || null
    };

    try {
        const response = await fetch('/api/notifications', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(notificationData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('notificationModal')).hide();
            loadNotifications();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ التنبيه:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// وظائف التصدير والطباعة
function exportEmployees() {
    showNotification('جاري تصدير بيانات الموظفين...', 'info');

    fetch('/api/employees')
        .then(response => response.json())
        .then(data => {
            exportToCSV(data, 'employees', 'بيانات_الموظفين');
            showNotification('تم تصدير بيانات الموظفين بنجاح', 'success');
        })
        .catch(error => {
            console.error('خطأ في تصدير الموظفين:', error);
            showNotification('خطأ في تصدير البيانات', 'error');
        });
}

function printEmployees() {
    showNotification('جاري تحضير الموظفين للطباعة...', 'info');

    fetch('/api/employees')
        .then(response => response.json())
        .then(data => {
            printTable(data, 'employees', 'قائمة الموظفين');
        })
        .catch(error => {
            console.error('خطأ في طباعة الموظفين:', error);
            showNotification('خطأ في طباعة البيانات', 'error');
        });
}

function exportService() {
    showNotification('جاري تصدير بيانات الخدمة...', 'info');

    fetch('/api/service')
        .then(response => response.json())
        .then(data => {
            exportToCSV(data, 'service', 'بيانات_الخدمة');
            showNotification('تم تصدير بيانات الخدمة بنجاح', 'success');
        })
        .catch(error => {
            console.error('خطأ في تصدير الخدمة:', error);
            showNotification('خطأ في تصدير البيانات', 'error');
        });
}

function printService() {
    showNotification('جاري تحضير الخدمة للطباعة...', 'info');

    fetch('/api/service')
        .then(response => response.json())
        .then(data => {
            printTable(data, 'service', 'قائمة خدمة الموظفين');
        })
        .catch(error => {
            console.error('خطأ في طباعة الخدمة:', error);
            showNotification('خطأ في طباعة البيانات', 'error');
        });
}

function exportCourses() {
    showNotification('جاري تصدير بيانات الدورات...', 'info');

    fetch('/api/courses')
        .then(response => response.json())
        .then(data => {
            exportToCSV(data, 'courses', 'بيانات_الدورات');
            showNotification('تم تصدير بيانات الدورات بنجاح', 'success');
        })
        .catch(error => {
            console.error('خطأ في تصدير الدورات:', error);
            showNotification('خطأ في تصدير البيانات', 'error');
        });
}

function printCourses() {
    showNotification('جاري تحضير الدورات للطباعة...', 'info');

    fetch('/api/courses')
        .then(response => response.json())
        .then(data => {
            printTable(data, 'courses', 'قائمة الدورات التدريبية');
        })
        .catch(error => {
            console.error('خطأ في طباعة الدورات:', error);
            showNotification('خطأ في طباعة البيانات', 'error');
        });
}

function exportLeaves() {
    showNotification('جاري تصدير بيانات الإجازات...', 'info');

    fetch('/api/leaves')
        .then(response => response.json())
        .then(data => {
            exportToCSV(data, 'leaves', 'بيانات_الإجازات');
            showNotification('تم تصدير بيانات الإجازات بنجاح', 'success');
        })
        .catch(error => {
            console.error('خطأ في تصدير الإجازات:', error);
            showNotification('خطأ في تصدير البيانات', 'error');
        });
}

function printLeaves() {
    showNotification('جاري تحضير الإجازات للطباعة...', 'info');

    fetch('/api/leaves')
        .then(response => response.json())
        .then(data => {
            printTable(data, 'leaves', 'قائمة الإجازات');
        })
        .catch(error => {
            console.error('خطأ في طباعة الإجازات:', error);
            showNotification('خطأ في طباعة البيانات', 'error');
        });
}

function exportDelegations() {
    showNotification('جاري تصدير بيانات الإيفادات...', 'info');

    fetch('/api/delegations')
        .then(response => response.json())
        .then(data => {
            exportToCSV(data, 'delegations', 'بيانات_الإيفادات');
            showNotification('تم تصدير بيانات الإيفادات بنجاح', 'success');
        })
        .catch(error => {
            console.error('خطأ في تصدير الإيفادات:', error);
            showNotification('خطأ في تصدير البيانات', 'error');
        });
}

function printDelegations() {
    showNotification('جاري تحضير الإيفادات للطباعة...', 'info');

    fetch('/api/delegations')
        .then(response => response.json())
        .then(data => {
            printTable(data, 'delegations', 'قائمة الإيفادات');
        })
        .catch(error => {
            console.error('خطأ في طباعة الإيفادات:', error);
            showNotification('خطأ في طباعة البيانات', 'error');
        });
}

// وظائف الحذف
async function deleteCourse(id) {
    if (!confirm('هل أنت متأكد من حذف هذه الدورة؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/courses/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadCourses();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الدورة', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الدورة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function deleteLeave(id) {
    if (!confirm('هل أنت متأكد من حذف هذه الإجازة؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/leaves/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadLeaves();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الإجازة', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الإجازة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function deleteDelegation(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الإيفاد؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/delegations/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadDelegations();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الإيفاد', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الإيفاد:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function markNotificationRead(id) {
    try {
        const response = await fetch(`/api/notifications/${id}/read`, {
            method: 'PUT'
        });

        const result = await response.json();

        if (result.success) {
            loadNotifications();
            loadStatistics();
        }
    } catch (error) {
        console.error('خطأ في تحديث التنبيه:', error);
    }
}

// وظائف الإعدادات
async function saveSettings() {
    const settingsData = {
        theme: document.getElementById('themeSelect').value,
        font: document.getElementById('fontSelect').value,
        font_size: document.getElementById('fontSizeSelect').value,
        theme_color: document.getElementById('themeColorSelect').value,
        notifications_enabled: document.getElementById('notificationsEnabled').checked ? 1 : 0,
        notification_duration: parseInt(document.getElementById('notificationDuration').value) * 1000,
        employee_notifications: document.getElementById('employeeNotifications').checked ? 1 : 0,
        leave_notifications: document.getElementById('leaveNotifications').checked ? 1 : 0,
        course_notifications: document.getElementById('courseNotifications').checked ? 1 : 0
    };

    try {
        const response = await fetch('/api/settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settingsData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            // تطبيق الإعدادات فوراً
            applySettings(settingsData);
        } else {
            showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

function applySettings(settings) {
    // تطبيق الثيم
    document.body.setAttribute('data-theme', settings.theme);

    // تطبيق الخط
    document.body.className = document.body.className.replace(/font-\w+/g, '');
    document.body.className += ` font-${settings.font}`;

    // تطبيق حجم الخط
    document.body.className = document.body.className.replace(/font-size-\w+/g, '');
    document.body.className += ` font-size-${settings.font_size}`;
}

function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        // إعادة تعيين القيم الافتراضية
        document.getElementById('themeSelect').value = 'light';
        document.getElementById('fontSelect').value = 'tajawal';
        document.getElementById('fontSizeSelect').value = 'medium';
        document.getElementById('themeColorSelect').value = 'default';
        document.getElementById('notificationsEnabled').checked = true;
        document.getElementById('notificationDuration').value = 5;
        document.getElementById('employeeNotifications').checked = true;
        document.getElementById('leaveNotifications').checked = true;
        document.getElementById('courseNotifications').checked = true;

        showNotification('تم إعادة تعيين الإعدادات', 'info');
    }
}

function showUserGuide() {
    const guideContent = `
        <div class="modal fade" id="userGuideModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title fw-bold">
                            <i class="bi bi-question-circle-fill me-2"></i>
                            دليل المستخدم
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6 class="fw-bold text-primary">إدارة الموظفين:</h6>
                        <ul>
                            <li>إضافة موظف جديد من خلال زر "إضافة موظف جديد"</li>
                            <li>تعديل بيانات الموظف بالضغط على أيقونة التعديل</li>
                            <li>حذف الموظف بالضغط على أيقونة الحذف</li>
                        </ul>

                        <h6 class="fw-bold text-success">معرفة الخدمة:</h6>
                        <ul>
                            <li>عرض مدة خدمة جميع الموظفين</li>
                            <li>يتم حساب مدة الخدمة تلقائياً</li>
                        </ul>

                        <h6 class="fw-bold text-info">الدورات التدريبية:</h6>
                        <ul>
                            <li>إضافة دورات تدريبية للموظفين</li>
                            <li>تحديد نوع ومدة ومكان الدورة</li>
                        </ul>

                        <h6 class="fw-bold text-warning">الإجازات:</h6>
                        <ul>
                            <li>تسجيل إجازات الموظفين</li>
                            <li>تحديد نوع ومدة الإجازة</li>
                        </ul>

                        <h6 class="fw-bold text-danger">الإيفادات:</h6>
                        <ul>
                            <li>تسجيل إيفادات الموظفين</li>
                            <li>تحديد الوجهة والغرض والمدة</li>
                        </ul>

                        <h6 class="fw-bold text-secondary">التنبيهات:</h6>
                        <ul>
                            <li>إضافة تنبيهات عامة أو خاصة بموظف معين</li>
                            <li>تحديد التنبيهات كمقروءة</li>
                        </ul>

                        <h6 class="fw-bold text-muted">الإعدادات:</h6>
                        <ul>
                            <li>تغيير الثيم (فاتح/داكن)</li>
                            <li>تغيير الخط وحجم الخط</li>
                            <li>إعدادات التنبيهات</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة النموذج السابق إن وجد
    const existingModal = document.getElementById('userGuideModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النموذج الجديد
    document.body.insertAdjacentHTML('beforeend', guideContent);

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('userGuideModal'));
    modal.show();
}

function showSettings() {
    // تفعيل تبويب الإعدادات
    const settingsTab = document.getElementById('settings-tab');
    settingsTab.click();
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        showNotification('تم تسجيل الخروج بنجاح', 'success');
        // يمكن إضافة منطق تسجيل الخروج هنا
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }
}

// وظائف التصدير والطباعة المساعدة
function exportToCSV(data, type, filename) {
    if (!data || data.length === 0) {
        showNotification('لا توجد بيانات للتصدير', 'warning');
        return;
    }

    let headers = [];
    let csvContent = '';

    // تحديد العناوين حسب نوع البيانات
    switch(type) {
        case 'employees':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'مكان العمل', 'تاريخ البداية', 'عنوان السكن', 'تاريخ الميلاد', 'رقم الجوال'];
            break;
        case 'service':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'مدة الخدمة'];
            break;
        case 'courses':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'اسم الدورة', 'نوع الدورة', 'المدة', 'مكان الدورة'];
            break;
        case 'leaves':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'مكان العمل', 'تاريخ الإجازة', 'مدة الإجازة'];
            break;
        case 'delegations':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'الوجهة', 'الدولة', 'الغرض', 'تاريخ البداية', 'تاريخ النهاية'];
            break;
        case 'books':
            headers = ['م', 'عنوان الكتاب', 'المؤلف', 'التصنيف', 'الناشر', 'سنة النشر', 'عدد الصفحات', 'اللغة', 'الموقع', 'الحالة'];
            break;
        default:
            headers = Object.keys(data[0] || {});
    }

    // إضافة العناوين
    csvContent += headers.join(',') + '\n';

    // إضافة البيانات
    data.forEach(row => {
        let values = [];
        switch(type) {
            case 'employees':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.work_location || '',
                    row.start_date || '',
                    row.address || '',
                    row.birth_date || '',
                    row.mobile || ''
                ];
                break;
            case 'service':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.service_duration || ''
                ];
                break;
            case 'courses':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.title || '',
                    row.type || '',
                    row.period || '',
                    row.location || ''
                ];
                break;
            case 'leaves':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.work_location || '',
                    row.leave_date || '',
                    row.leave_duration || ''
                ];
                break;
            case 'delegations':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.destination || '',
                    row.country || '',
                    row.purpose || '',
                    row.start_date || '',
                    row.end_date || ''
                ];
                break;
            case 'books':
                values = [
                    row.sequence || '',
                    row.title || '',
                    row.author || '',
                    row.category || '',
                    row.publisher || '',
                    row.publication_year || '',
                    row.pages || '',
                    row.language || '',
                    row.location || '',
                    row.status || ''
                ];
                break;
            default:
                values = Object.values(row);
        }

        // تنظيف البيانات وإضافة علامات اقتباس للنصوص التي تحتوي على فواصل
        const cleanValues = values.map(value => {
            const str = String(value || '');
            return str.includes(',') ? `"${str}"` : str;
        });

        csvContent += cleanValues.join(',') + '\n';
    });

    // إنشاء وتحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function printTable(data, type, title) {
    if (!data || data.length === 0) {
        showNotification('لا توجد بيانات للطباعة', 'warning');
        return;
    }

    let headers = [];
    let tableHTML = '';

    // تحديد العناوين حسب نوع البيانات
    switch(type) {
        case 'employees':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'مكان العمل', 'تاريخ البداية', 'عنوان السكن', 'تاريخ الميلاد', 'رقم الجوال'];
            break;
        case 'service':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'مدة الخدمة'];
            break;
        case 'courses':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'اسم الدورة', 'نوع الدورة', 'المدة', 'مكان الدورة'];
            break;
        case 'leaves':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'مكان العمل', 'تاريخ الإجازة', 'مدة الإجازة'];
            break;
        case 'delegations':
            headers = ['م', 'رقم الشركة', 'الاسم الكامل', 'المسمى الوظيفي', 'الوجهة', 'الدولة', 'الغرض', 'تاريخ البداية', 'تاريخ النهاية'];
            break;
        case 'books':
            headers = ['م', 'عنوان الكتاب', 'المؤلف', 'التصنيف', 'الناشر', 'سنة النشر', 'عدد الصفحات', 'اللغة', 'الموقع', 'الحالة'];
            break;
        default:
            headers = Object.keys(data[0] || {});
    }

    // إنشاء جدول HTML
    tableHTML = `
        <table style="width: 100%; border-collapse: collapse; font-family: 'Tajawal', Arial, sans-serif; direction: rtl;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    ${headers.map(header => `<th style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">${header}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
    `;

    // إضافة البيانات
    data.forEach(row => {
        let values = [];
        switch(type) {
            case 'employees':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.work_location || '',
                    row.start_date || '',
                    row.address || '',
                    row.birth_date || '',
                    row.mobile || ''
                ];
                break;
            case 'service':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.service_duration || ''
                ];
                break;
            case 'courses':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.title || '',
                    row.type || '',
                    row.period || '',
                    row.location || ''
                ];
                break;
            case 'leaves':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.work_location || '',
                    row.leave_date || '',
                    row.leave_duration || ''
                ];
                break;
            case 'delegations':
                values = [
                    row.sequence || '',
                    row.company_number || '',
                    row.full_name || '',
                    row.job_title || '',
                    row.destination || '',
                    row.country || '',
                    row.purpose || '',
                    row.start_date || '',
                    row.end_date || ''
                ];
                break;
            case 'books':
                values = [
                    row.sequence || '',
                    row.title || '',
                    row.author || '',
                    row.category || '',
                    row.publisher || '',
                    row.publication_year || '',
                    row.pages || '',
                    row.language || '',
                    row.location || '',
                    row.status || ''
                ];
                break;
            default:
                values = Object.values(row);
        }

        tableHTML += `
            <tr>
                ${values.map(value => `<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${value || ''}</td>`).join('')}
            </tr>
        `;
    });

    tableHTML += `
            </tbody>
        </table>
    `;

    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>${title}</title>
            <style>
                body {
                    font-family: 'Tajawal', Arial, sans-serif;
                    direction: rtl;
                    margin: 20px;
                }
                .header {
                    text-align: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #333;
                    padding-bottom: 10px;
                }
                .print-date {
                    text-align: left;
                    font-size: 12px;
                    color: #666;
                    margin-bottom: 10px;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</div>
            <div class="header">
                <h1>${title}</h1>
                <h3>نظام إدارة الموظفين</h3>
            </div>
            ${tableHTML}
            <script>
                window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                        window.close();
                    };
                };
            </script>
        </body>
        </html>
    `);
    printWindow.document.close();
}

// وظائف الإجراءات السريعة
function refreshDashboard() {
    showNotification('جاري تحديث لوحة التحكم...', 'info');
    loadDashboard();
    loadStatistics();
    showNotification('تم تحديث لوحة التحكم بنجاح', 'success');
}

function showQuickActions() {
    const quickActionsModal = `
        <div class="modal fade" id="quickActionsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title fw-bold">
                            <i class="bi bi-lightning-fill me-2"></i>
                            الإجراءات السريعة
                        </h5>
                    </div>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-6">
                                <button class="btn btn-outline-primary w-100" onclick="showAddEmployeeModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-person-plus-fill d-block fs-1 mb-2"></i>
                                    إضافة موظف
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-success w-100" onclick="showAddCourseModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-book-fill d-block fs-1 mb-2"></i>
                                    إضافة دورة
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-warning w-100" onclick="showAddLeaveModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-calendar-check-fill d-block fs-1 mb-2"></i>
                                    إضافة إجازة
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-danger w-100" onclick="showAddDelegationModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-airplane-fill d-block fs-1 mb-2"></i>
                                    إضافة إيفاد
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-purple w-100" onclick="showAddBookModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-book-half d-block fs-1 mb-2"></i>
                                    إضافة كتاب
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-secondary w-100" onclick="showAddNotificationModal(); bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();">
                                    <i class="bi bi-bell-fill d-block fs-1 mb-2"></i>
                                    إضافة تنبيه
                                </button>
                            </div>
                        </div>
                        <hr>
                        <div class="row g-2">
                            <div class="col-4">
                                <button class="btn btn-outline-info btn-sm w-100" onclick="exportAllData()">
                                    <i class="bi bi-download me-1"></i>
                                    تصدير الكل
                                </button>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-outline-dark btn-sm w-100" onclick="printAllData()">
                                    <i class="bi bi-printer-fill me-1"></i>
                                    طباعة الكل
                                </button>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-outline-success btn-sm w-100" onclick="backupData()">
                                    <i class="bi bi-shield-check me-1"></i>
                                    نسخ احتياطي
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-2"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة النموذج السابق إن وجد
    const existingModal = document.getElementById('quickActionsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النموذج الجديد
    document.body.insertAdjacentHTML('beforeend', quickActionsModal);

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('quickActionsModal'));
    modal.show();
}

// وظائف إضافية للإجراءات السريعة
function exportAllData() {
    showNotification('جاري تصدير جميع البيانات...', 'info');

    Promise.all([
        fetch('/api/employees').then(r => r.json()),
        fetch('/api/courses').then(r => r.json()),
        fetch('/api/leaves').then(r => r.json()),
        fetch('/api/delegations').then(r => r.json()),
        fetch('/api/books').then(r => r.json())
    ]).then(([employees, courses, leaves, delegations, books]) => {
        exportToCSV(employees, 'employees', 'جميع_الموظفين');
        setTimeout(() => exportToCSV(courses, 'courses', 'جميع_الدورات'), 500);
        setTimeout(() => exportToCSV(leaves, 'leaves', 'جميع_الإجازات'), 1000);
        setTimeout(() => exportToCSV(delegations, 'delegations', 'جميع_الإيفادات'), 1500);
        setTimeout(() => exportToCSV(books, 'books', 'جميع_الكتب'), 2000);

        showNotification('تم تصدير جميع البيانات بنجاح', 'success');
    }).catch(error => {
        console.error('خطأ في تصدير البيانات:', error);
        showNotification('خطأ في تصدير البيانات', 'error');
    });
}

function printAllData() {
    showNotification('جاري تحضير جميع البيانات للطباعة...', 'info');

    Promise.all([
        fetch('/api/employees').then(r => r.json()),
        fetch('/api/courses').then(r => r.json()),
        fetch('/api/leaves').then(r => r.json()),
        fetch('/api/delegations').then(r => r.json()),
        fetch('/api/books').then(r => r.json())
    ]).then(([employees, courses, leaves, delegations, books]) => {
        // طباعة كل قسم في نافذة منفصلة
        printTable(employees, 'employees', 'قائمة جميع الموظفين');
        setTimeout(() => printTable(courses, 'courses', 'قائمة جميع الدورات'), 1000);
        setTimeout(() => printTable(leaves, 'leaves', 'قائمة جميع الإجازات'), 2000);
        setTimeout(() => printTable(delegations, 'delegations', 'قائمة جميع الإيفادات'), 3000);
        setTimeout(() => printTable(books, 'books', 'قائمة جميع الكتب'), 4000);

        showNotification('تم تحضير جميع البيانات للطباعة', 'success');
    }).catch(error => {
        console.error('خطأ في طباعة البيانات:', error);
        showNotification('خطأ في طباعة البيانات', 'error');
    });
}

function backupData() {
    showNotification('جاري إنشاء نسخة احتياطية...', 'info');

    Promise.all([
        fetch('/api/employees').then(r => r.json()),
        fetch('/api/courses').then(r => r.json()),
        fetch('/api/leaves').then(r => r.json()),
        fetch('/api/delegations').then(r => r.json()),
        fetch('/api/books').then(r => r.json()),
        fetch('/api/notifications').then(r => r.json()),
        fetch('/api/statistics').then(r => r.json())
    ]).then(([employees, courses, leaves, delegations, books, notifications, statistics]) => {
        const backupData = {
            timestamp: new Date().toISOString(),
            version: '1.0',
            data: {
                employees,
                courses,
                leaves,
                delegations,
                books,
                notifications,
                statistics
            }
        };

        const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `backup_${new Date().toISOString().split('T')[0]}.json`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    }).catch(error => {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        showNotification('خطأ في إنشاء النسخة الاحتياطية', 'error');
    });
}

// وظيفة إضافة التنبيهات
function showAddNotificationModal() {
    currentNotificationId = null;
    document.getElementById('notificationModalTitle').textContent = 'إضافة تنبيه جديد';
    document.getElementById('notificationForm').reset();
    document.getElementById('notificationId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
    modal.show();
}

// وظائف البحث والفلترة
function setupSearchAndFilters() {
    // البحث في الموظفين
    const employeesSearch = document.getElementById('employeesSearch');
    if (employeesSearch) {
        employeesSearch.addEventListener('input', function() {
            filterTable('employeesTableBody', this.value, ['full_name', 'company_number', 'job_title']);
        });
    }

    // البحث في الدورات
    const coursesSearch = document.getElementById('coursesSearch');
    if (coursesSearch) {
        coursesSearch.addEventListener('input', function() {
            filterTable('coursesTableBody', this.value, ['full_name', 'title', 'type']);
        });
    }

    // البحث في الإجازات
    const leavesSearch = document.getElementById('leavesSearch');
    if (leavesSearch) {
        leavesSearch.addEventListener('input', function() {
            filterTable('leavesTableBody', this.value, ['full_name', 'company_number']);
        });
    }

    // البحث في الإيفادات
    const delegationsSearch = document.getElementById('delegationsSearch');
    if (delegationsSearch) {
        delegationsSearch.addEventListener('input', function() {
            filterTable('delegationsTableBody', this.value, ['full_name', 'destination', 'purpose']);
        });
    }

    // البحث في الكتب
    const booksSearch = document.getElementById('booksSearch');
    if (booksSearch) {
        booksSearch.addEventListener('input', function() {
            filterTable('booksTableBody', this.value, ['title', 'author', 'category']);
        });
    }
}

function filterTable(tableBodyId, searchTerm, searchFields) {
    const tableBody = document.getElementById(tableBodyId);
    if (!tableBody) return;

    const rows = tableBody.getElementsByTagName('tr');
    const term = searchTerm.toLowerCase();

    for (let row of rows) {
        let shouldShow = false;

        if (term === '') {
            shouldShow = true;
        } else {
            const cells = row.getElementsByTagName('td');
            for (let cell of cells) {
                if (cell.textContent.toLowerCase().includes(term)) {
                    shouldShow = true;
                    break;
                }
            }
        }

        row.style.display = shouldShow ? '' : 'none';
    }
}

// تفعيل البحث والفلاتر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setupSearchAndFilters();
});
