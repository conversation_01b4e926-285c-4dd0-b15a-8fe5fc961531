// نظام إدارة الموظفين - الوظائف التفاعلية

// المتغيرات العامة
let currentEmployeeId = null;
let currentCourseId = null;
let currentLeaveId = null;
let currentDelegationId = null;
let employees = [];
let settings = {};

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
function initializeApp() {
    loadSettings();
    loadStatistics();
    loadEmployees();
    loadService();
    loadCourses();
    loadLeaves();
    loadDelegations();
    loadNotifications();

    // تفعيل التبويبات
    const tabTriggerList = document.querySelectorAll('#mainTabs button[data-bs-toggle="tab"]');
    tabTriggerList.forEach(tabTrigger => {
        tabTrigger.addEventListener('shown.bs.tab', function(event) {
            const target = event.target.getAttribute('data-bs-target');
            handleTabChange(target);
        });
    });
}

// التعامل مع تغيير التبويبات
function handleTabChange(target) {
    switch(target) {
        case '#employees':
            loadEmployees();
            break;
        case '#service':
            loadService();
            break;
        case '#courses':
            loadCourses();
            break;
        case '#leaves':
            loadLeaves();
            break;
        case '#delegations':
            loadDelegations();
            break;
        case '#notifications':
            loadNotifications();
            break;
        case '#settings':
            loadSettings();
            break;
    }
}

// تحميل الإحصائيات
async function loadStatistics() {
    try {
        const response = await fetch('/api/statistics');
        const stats = await response.json();

        document.getElementById('employeesCount').textContent = stats.employees_count;
        document.getElementById('coursesCount').textContent = stats.courses_count;
        document.getElementById('leavesCount').textContent = stats.leaves_count;
        document.getElementById('delegationsCount').textContent = stats.delegations_count;

        // تحديث شارة التنبيهات
        const notificationsBadge = document.getElementById('notificationsBadge');
        if (stats.unread_notifications > 0) {
            notificationsBadge.textContent = stats.unread_notifications;
            notificationsBadge.style.display = 'inline';
        } else {
            notificationsBadge.style.display = 'none';
        }
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
        showNotification('خطأ في تحميل الإحصائيات', 'error');
    }
}

// تحميل الموظفين
async function loadEmployees() {
    try {
        const response = await fetch('/api/employees');
        employees = await response.json();

        const tbody = document.getElementById('employeesTableBody');
        tbody.innerHTML = '';

        employees.forEach(employee => {
            const row = createEmployeeRow(employee);
            tbody.appendChild(row);
        });

        // تحديث قوائم الموظفين في النماذج
        updateEmployeeSelects();
    } catch (error) {
        console.error('خطأ في تحميل الموظفين:', error);
        showNotification('خطأ في تحميل بيانات الموظفين', 'error');
    }
}

// إنشاء صف موظف
function createEmployeeRow(employee) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${employee.sequence}</td>
        <td>${employee.company_number}</td>
        <td>${employee.full_name}</td>
        <td>${employee.job_title}</td>
        <td>${employee.work_location}</td>
        <td>${formatDate(employee.start_date)}</td>
        <td>${employee.address || '-'}</td>
        <td>${formatDate(employee.birth_date) || '-'}</td>
        <td>${employee.mobile || '-'}</td>
        <td>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="editEmployee(${employee.id})" title="تعديل">
                    <i class="bi bi-pencil-fill"></i>
                </button>
                <button class="btn btn-outline-danger" onclick="deleteEmployee(${employee.id})" title="حذف">
                    <i class="bi bi-trash-fill"></i>
                </button>
            </div>
        </td>
    `;
    return row;
}

// تحديث قوائم الموظفين في النماذج
function updateEmployeeSelects() {
    const selects = [
        'courseEmployeeId',
        'leaveEmployeeId',
        'delegationEmployeeId',
        'notificationEmployeeId'
    ];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // الاحتفاظ بالخيار الأول
            const firstOption = select.querySelector('option[value=""]');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.company_number} - ${employee.full_name}`;
                select.appendChild(option);
            });
        }
    });
}

// عرض نموذج إضافة موظف
function showAddEmployeeModal() {
    currentEmployeeId = null;
    document.getElementById('employeeModalTitle').textContent = 'إضافة موظف جديد';
    document.getElementById('employeeForm').reset();
    document.getElementById('employeeId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
    modal.show();
}

// تعديل موظف
function editEmployee(id) {
    const employee = employees.find(emp => emp.id === id);
    if (!employee) return;

    currentEmployeeId = id;
    document.getElementById('employeeModalTitle').textContent = 'تعديل بيانات الموظف';

    // ملء النموذج
    document.getElementById('employeeId').value = employee.id;
    document.getElementById('companyNumber').value = employee.company_number;
    document.getElementById('fullName').value = employee.full_name;
    document.getElementById('jobTitle').value = employee.job_title;
    document.getElementById('workLocation').value = employee.work_location;
    document.getElementById('startDate').value = employee.start_date;
    document.getElementById('birthDate').value = employee.birth_date || '';
    document.getElementById('address').value = employee.address || '';
    document.getElementById('mobile').value = employee.mobile || '';

    const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
    modal.show();
}

// حفظ الموظف
async function saveEmployee() {
    const form = document.getElementById('employeeForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const employeeData = {
        company_number: document.getElementById('companyNumber').value,
        full_name: document.getElementById('fullName').value,
        job_title: document.getElementById('jobTitle').value,
        work_location: document.getElementById('workLocation').value,
        start_date: document.getElementById('startDate').value,
        birth_date: document.getElementById('birthDate').value,
        address: document.getElementById('address').value,
        mobile: document.getElementById('mobile').value
    };

    try {
        let response;
        if (currentEmployeeId) {
            // تحديث
            response = await fetch(`/api/employees/${currentEmployeeId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(employeeData)
            });
        } else {
            // إضافة جديد
            response = await fetch('/api/employees', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(employeeData)
            });
        }

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('employeeModal')).hide();
            loadEmployees();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الموظف:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// حذف موظف
async function deleteEmployee(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الموظف؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        return;
    }

    try {
        const response = await fetch(`/api/employees/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadEmployees();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الموظف', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الموظف:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// تحميل بيانات الخدمة
async function loadService() {
    try {
        const response = await fetch('/api/service');
        const serviceData = await response.json();

        const tbody = document.getElementById('serviceTableBody');
        tbody.innerHTML = '';

        serviceData.forEach(service => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${service.sequence}</td>
                <td>${service.company_number}</td>
                <td>${service.full_name}</td>
                <td>${service.job_title}</td>
                <td class="fw-bold text-primary">${service.service_duration}</td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل بيانات الخدمة:', error);
        showNotification('خطأ في تحميل بيانات الخدمة', 'error');
    }
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// عرض التنبيهات
function showNotification(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';

    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}-fill me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تبديل الثيم
function toggleTheme() {
    const body = document.body;
    const currentTheme = body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);

    showNotification(`تم تغيير الثيم إلى ${newTheme === 'dark' ? 'الداكن' : 'الفاتح'}`, 'success');
}

// تحميل الثيم المحفوظ
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.body.setAttribute('data-theme', savedTheme);
}

// تحميل الثيم عند بدء التطبيق
loadSavedTheme();

// تحميل الدورات
async function loadCourses() {
    try {
        const response = await fetch('/api/courses');
        const courses = await response.json();

        const tbody = document.getElementById('coursesTableBody');
        tbody.innerHTML = '';

        courses.forEach(course => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${course.sequence}</td>
                <td>${course.company_number}</td>
                <td>${course.full_name}</td>
                <td>${course.job_title}</td>
                <td>${course.title}</td>
                <td>${course.type}</td>
                <td>${course.period}</td>
                <td>${course.location}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="editCourse(${course.id})" title="تعديل">
                            <i class="bi bi-pencil-fill"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteCourse(${course.id})" title="حذف">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل الدورات:', error);
        showNotification('خطأ في تحميل بيانات الدورات', 'error');
    }
}

// تحميل الإجازات
async function loadLeaves() {
    try {
        const response = await fetch('/api/leaves');
        const leaves = await response.json();

        const tbody = document.getElementById('leavesTableBody');
        tbody.innerHTML = '';

        leaves.forEach(leave => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${leave.sequence}</td>
                <td>${leave.company_number}</td>
                <td>${leave.full_name}</td>
                <td>${leave.job_title}</td>
                <td>${leave.work_location}</td>
                <td>${formatDate(leave.leave_date)}</td>
                <td>${leave.leave_duration}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-warning" onclick="editLeave(${leave.id})" title="تعديل">
                            <i class="bi bi-pencil-fill"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteLeave(${leave.id})" title="حذف">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل الإجازات:', error);
        showNotification('خطأ في تحميل بيانات الإجازات', 'error');
    }
}

// تحميل الإيفادات
async function loadDelegations() {
    try {
        const response = await fetch('/api/delegations');
        const delegations = await response.json();

        const tbody = document.getElementById('delegationsTableBody');
        tbody.innerHTML = '';

        delegations.forEach(delegation => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${delegation.sequence}</td>
                <td>${delegation.company_number}</td>
                <td>${delegation.full_name}</td>
                <td>${delegation.job_title}</td>
                <td>${delegation.destination}</td>
                <td>${delegation.country}</td>
                <td>${delegation.purpose}</td>
                <td>${formatDate(delegation.start_date)}</td>
                <td>${formatDate(delegation.end_date)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-danger" onclick="deleteDelegation(${delegation.id})" title="حذف">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل الإيفادات:', error);
        showNotification('خطأ في تحميل بيانات الإيفادات', 'error');
    }
}

// تحميل التنبيهات
async function loadNotifications() {
    try {
        const response = await fetch('/api/notifications');
        const notifications = await response.json();

        const tbody = document.getElementById('notificationsTableBody');
        tbody.innerHTML = '';

        notifications.forEach(notification => {
            const row = document.createElement('tr');
            const isRead = notification.read === 1;

            row.className = isRead ? '' : 'table-warning';
            row.innerHTML = `
                <td>${notification.sequence}</td>
                <td>${notification.title}</td>
                <td>${notification.message}</td>
                <td><span class="badge bg-secondary">${notification.type}</span></td>
                <td>${formatDate(notification.date)}</td>
                <td>${notification.employee_name || '-'}</td>
                <td>
                    <span class="badge ${isRead ? 'bg-success' : 'bg-warning'}">
                        ${isRead ? 'مقروء' : 'غير مقروء'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        ${!isRead ? `<button class="btn btn-outline-success" onclick="markNotificationRead(${notification.id})" title="تحديد كمقروء">
                            <i class="bi bi-check-circle-fill"></i>
                        </button>` : ''}
                        <button class="btn btn-outline-danger" onclick="deleteNotification(${notification.id})" title="حذف">
                            <i class="bi bi-trash-fill"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('خطأ في تحميل التنبيهات:', error);
        showNotification('خطأ في تحميل التنبيهات', 'error');
    }
}

// تحميل الإعدادات
async function loadSettings() {
    try {
        const response = await fetch('/api/settings');
        settings = await response.json();

        // تطبيق الإعدادات على الواجهة
        if (settings.theme) {
            document.body.setAttribute('data-theme', settings.theme);
            document.getElementById('themeSelect').value = settings.theme;
        }

        if (settings.font) {
            document.body.className = `font-${settings.font} ${document.body.className}`;
            document.getElementById('fontSelect').value = settings.font;
        }

        if (settings.font_size) {
            document.body.className = `font-size-${settings.font_size} ${document.body.className}`;
            document.getElementById('fontSizeSelect').value = settings.font_size;
        }

        if (settings.theme_color) {
            document.getElementById('themeColorSelect').value = settings.theme_color;
        }

        // إعدادات التنبيهات
        document.getElementById('notificationsEnabled').checked = settings.notifications_enabled === 1;
        document.getElementById('notificationDuration').value = settings.notification_duration / 1000;
        document.getElementById('employeeNotifications').checked = settings.employee_notifications === 1;
        document.getElementById('leaveNotifications').checked = settings.leave_notifications === 1;
        document.getElementById('courseNotifications').checked = settings.course_notifications === 1;

    } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
        showNotification('خطأ في تحميل الإعدادات', 'error');
    }
}

// وظائف النماذج المنبثقة
function showAddCourseModal() {
    currentCourseId = null;
    document.getElementById('courseForm').reset();
    updateEmployeeSelects();

    const modal = new bootstrap.Modal(document.getElementById('courseModal'));
    modal.show();
}

function showAddLeaveModal() {
    currentLeaveId = null;
    document.getElementById('leaveForm').reset();
    updateEmployeeSelects();

    const modal = new bootstrap.Modal(document.getElementById('leaveModal'));
    modal.show();
}

function showAddDelegationModal() {
    currentDelegationId = null;
    document.getElementById('delegationForm').reset();
    updateEmployeeSelects();

    const modal = new bootstrap.Modal(document.getElementById('delegationModal'));
    modal.show();
}

function showAddNotificationModal() {
    document.getElementById('notificationForm').reset();
    updateEmployeeSelects();

    // تعيين التاريخ الحالي
    document.getElementById('notificationDate').value = new Date().toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
    modal.show();
}

// وظائف الحفظ
async function saveCourse() {
    const form = document.getElementById('courseForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const courseData = {
        employee_id: document.getElementById('courseEmployeeId').value,
        title: document.getElementById('courseTitle').value,
        type: document.getElementById('courseType').value,
        period: document.getElementById('coursePeriod').value,
        location: document.getElementById('courseLocation').value,
        start_date: document.getElementById('courseStartDate').value
    };

    try {
        const response = await fetch('/api/courses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(courseData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('courseModal')).hide();
            loadCourses();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الدورة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function saveLeave() {
    const form = document.getElementById('leaveForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const leaveData = {
        employee_id: document.getElementById('leaveEmployeeId').value,
        leave_type: document.getElementById('leaveType').value,
        leave_date: document.getElementById('leaveDate').value,
        leave_duration: document.getElementById('leaveDuration').value,
        leave_reason: document.getElementById('leaveReason').value
    };

    try {
        const response = await fetch('/api/leaves', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(leaveData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('leaveModal')).hide();
            loadLeaves();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الإجازة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function saveDelegation() {
    const form = document.getElementById('delegationForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const delegationData = {
        employee_id: document.getElementById('delegationEmployeeId').value,
        destination: document.getElementById('delegationDestination').value,
        country: document.getElementById('delegationCountry').value,
        purpose: document.getElementById('delegationPurpose').value,
        start_date: document.getElementById('delegationStartDate').value,
        end_date: document.getElementById('delegationEndDate').value
    };

    try {
        const response = await fetch('/api/delegations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(delegationData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('delegationModal')).hide();
            loadDelegations();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الإيفاد:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function saveNotification() {
    const form = document.getElementById('notificationForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const notificationData = {
        title: document.getElementById('notificationTitle').value,
        message: document.getElementById('notificationMessage').value,
        type: document.getElementById('notificationType').value,
        date: document.getElementById('notificationDate').value,
        employee_id: document.getElementById('notificationEmployeeId').value || null
    };

    try {
        const response = await fetch('/api/notifications', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(notificationData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('notificationModal')).hide();
            loadNotifications();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ التنبيه:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// وظائف التصدير والطباعة
function exportEmployees() {
    console.log('تصدير الموظفين');
}

function printEmployees() {
    console.log('طباعة الموظفين');
}

function exportService() {
    console.log('تصدير الخدمة');
}

function printService() {
    console.log('طباعة الخدمة');
}

function exportCourses() {
    console.log('تصدير الدورات');
}

function printCourses() {
    console.log('طباعة الدورات');
}

function exportLeaves() {
    console.log('تصدير الإجازات');
}

function printLeaves() {
    console.log('طباعة الإجازات');
}

function exportDelegations() {
    console.log('تصدير الإيفادات');
}

function printDelegations() {
    console.log('طباعة الإيفادات');
}

// وظائف الحذف
async function deleteCourse(id) {
    if (!confirm('هل أنت متأكد من حذف هذه الدورة؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/courses/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadCourses();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الدورة', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الدورة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function deleteLeave(id) {
    if (!confirm('هل أنت متأكد من حذف هذه الإجازة؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/leaves/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadLeaves();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الإجازة', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الإجازة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function deleteDelegation(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الإيفاد؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/delegations/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            loadDelegations();
            loadStatistics();
        } else {
            showNotification('حدث خطأ أثناء حذف الإيفاد', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف الإيفاد:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

async function markNotificationRead(id) {
    try {
        const response = await fetch(`/api/notifications/${id}/read`, {
            method: 'PUT'
        });

        const result = await response.json();

        if (result.success) {
            loadNotifications();
            loadStatistics();
        }
    } catch (error) {
        console.error('خطأ في تحديث التنبيه:', error);
    }
}

// وظائف الإعدادات
async function saveSettings() {
    const settingsData = {
        theme: document.getElementById('themeSelect').value,
        font: document.getElementById('fontSelect').value,
        font_size: document.getElementById('fontSizeSelect').value,
        theme_color: document.getElementById('themeColorSelect').value,
        notifications_enabled: document.getElementById('notificationsEnabled').checked ? 1 : 0,
        notification_duration: parseInt(document.getElementById('notificationDuration').value) * 1000,
        employee_notifications: document.getElementById('employeeNotifications').checked ? 1 : 0,
        leave_notifications: document.getElementById('leaveNotifications').checked ? 1 : 0,
        course_notifications: document.getElementById('courseNotifications').checked ? 1 : 0
    };

    try {
        const response = await fetch('/api/settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settingsData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            // تطبيق الإعدادات فوراً
            applySettings(settingsData);
        } else {
            showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
        }
    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

function applySettings(settings) {
    // تطبيق الثيم
    document.body.setAttribute('data-theme', settings.theme);

    // تطبيق الخط
    document.body.className = document.body.className.replace(/font-\w+/g, '');
    document.body.className += ` font-${settings.font}`;

    // تطبيق حجم الخط
    document.body.className = document.body.className.replace(/font-size-\w+/g, '');
    document.body.className += ` font-size-${settings.font_size}`;
}

function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        // إعادة تعيين القيم الافتراضية
        document.getElementById('themeSelect').value = 'light';
        document.getElementById('fontSelect').value = 'tajawal';
        document.getElementById('fontSizeSelect').value = 'medium';
        document.getElementById('themeColorSelect').value = 'default';
        document.getElementById('notificationsEnabled').checked = true;
        document.getElementById('notificationDuration').value = 5;
        document.getElementById('employeeNotifications').checked = true;
        document.getElementById('leaveNotifications').checked = true;
        document.getElementById('courseNotifications').checked = true;

        showNotification('تم إعادة تعيين الإعدادات', 'info');
    }
}

function showUserGuide() {
    const guideContent = `
        <div class="modal fade" id="userGuideModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title fw-bold">
                            <i class="bi bi-question-circle-fill me-2"></i>
                            دليل المستخدم
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6 class="fw-bold text-primary">إدارة الموظفين:</h6>
                        <ul>
                            <li>إضافة موظف جديد من خلال زر "إضافة موظف جديد"</li>
                            <li>تعديل بيانات الموظف بالضغط على أيقونة التعديل</li>
                            <li>حذف الموظف بالضغط على أيقونة الحذف</li>
                        </ul>

                        <h6 class="fw-bold text-success">معرفة الخدمة:</h6>
                        <ul>
                            <li>عرض مدة خدمة جميع الموظفين</li>
                            <li>يتم حساب مدة الخدمة تلقائياً</li>
                        </ul>

                        <h6 class="fw-bold text-info">الدورات التدريبية:</h6>
                        <ul>
                            <li>إضافة دورات تدريبية للموظفين</li>
                            <li>تحديد نوع ومدة ومكان الدورة</li>
                        </ul>

                        <h6 class="fw-bold text-warning">الإجازات:</h6>
                        <ul>
                            <li>تسجيل إجازات الموظفين</li>
                            <li>تحديد نوع ومدة الإجازة</li>
                        </ul>

                        <h6 class="fw-bold text-danger">الإيفادات:</h6>
                        <ul>
                            <li>تسجيل إيفادات الموظفين</li>
                            <li>تحديد الوجهة والغرض والمدة</li>
                        </ul>

                        <h6 class="fw-bold text-secondary">التنبيهات:</h6>
                        <ul>
                            <li>إضافة تنبيهات عامة أو خاصة بموظف معين</li>
                            <li>تحديد التنبيهات كمقروءة</li>
                        </ul>

                        <h6 class="fw-bold text-muted">الإعدادات:</h6>
                        <ul>
                            <li>تغيير الثيم (فاتح/داكن)</li>
                            <li>تغيير الخط وحجم الخط</li>
                            <li>إعدادات التنبيهات</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة النموذج السابق إن وجد
    const existingModal = document.getElementById('userGuideModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النموذج الجديد
    document.body.insertAdjacentHTML('beforeend', guideContent);

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('userGuideModal'));
    modal.show();
}

function showSettings() {
    // تفعيل تبويب الإعدادات
    const settingsTab = document.getElementById('settings-tab');
    settingsTab.click();
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        showNotification('تم تسجيل الخروج بنجاح', 'success');
        // يمكن إضافة منطق تسجيل الخروج هنا
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }
}
