@echo off
chcp 65001 >nul
echo ============================================================
echo           نظام إدارة الموظفين - الإصدار المُصحح
echo ============================================================
echo.
echo 🚀 بدء تشغيل النظام...
echo.

cd /d "%~dp0"

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متاح
    echo 💡 قم بتثبيت Python من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo 🔧 تشغيل الإصدار المُصحح...
python fixed_app.py

if errorlevel 1 (
    echo.
    echo ⚠️ فشل تشغيل الإصدار المُصحح، جاري المحاولة مع الإصدار الأصلي...
    python app.py

    if errorlevel 1 (
        echo.
        echo ❌ خطأ في تشغيل النظام
        echo 💡 تأكد من:
        echo    - تثبيت Python بشكل صحيح
        echo    - تثبيت Flask: pip install flask flask-cors
        echo    - صلاحيات الملفات
        echo.
        pause
    )
)

echo.
echo 👋 تم إنهاء النظام
pause
