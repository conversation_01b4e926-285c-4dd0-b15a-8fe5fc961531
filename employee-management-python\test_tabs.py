#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار شريط التبويبات - نظام إدارة الموظفين
"""

import os
import sys
import webbrowser
import threading
import time

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from flask import Flask, render_template, jsonify
    from flask_cors import CORS
    print("✅ تم تحميل Flask بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد Flask: {e}")
    print("💡 قم بتثبيت Flask: pip install flask flask-cors")
    input("اضغط Enter للخروج...")
    sys.exit(1)

def create_test_app():
    """إنشاء تطبيق اختبار"""
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/')
    def index():
        return render_template('index.html')
    
    @app.route('/api/statistics')
    def statistics():
        return jsonify({
            'employees_count': 15,
            'courses_count': 8,
            'leaves_count': 3,
            'delegations_count': 2,
            'books_count': 25,
            'borrowed_books': 5,
            'unread_notifications': 1
        })
    
    @app.route('/api/employees')
    def employees():
        return jsonify([
            {
                'id': 1,
                'sequence': 1,
                'company_number': 'EMP001',
                'full_name': 'أحمد محمد علي',
                'job_title': 'مطور برمجيات',
                'work_location': 'الرياض',
                'start_date': '2023-01-15',
                'address': 'الرياض - حي النرجس',
                'birth_date': '1990-05-20',
                'mobile': '0501234567'
            },
            {
                'id': 2,
                'sequence': 2,
                'company_number': 'EMP002',
                'full_name': 'فاطمة خالد السعد',
                'job_title': 'محاسبة',
                'work_location': 'جدة',
                'start_date': '2023-02-01',
                'address': 'جدة - حي الزهراء',
                'birth_date': '1988-08-12',
                'mobile': '0509876543'
            }
        ])
    
    @app.route('/api/service')
    def service():
        return jsonify([
            {
                'id': 1,
                'sequence': 1,
                'company_number': 'EMP001',
                'full_name': 'أحمد محمد علي',
                'job_title': 'مطور برمجيات',
                'service_duration': '1 سنة و 2 شهر و 15 يوم'
            }
        ])
    
    # APIs فارغة للباقي
    @app.route('/api/courses')
    def courses():
        return jsonify([])
    
    @app.route('/api/leaves')
    def leaves():
        return jsonify([])
    
    @app.route('/api/delegations')
    def delegations():
        return jsonify([])
    
    @app.route('/api/books')
    def books():
        return jsonify([])
    
    @app.route('/api/notifications')
    def notifications():
        return jsonify([])
    
    return app

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح")
    except:
        print("⚠️ افتح المتصفح يدوياً على: http://localhost:5000")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شريط التبويبات - نظام إدارة الموظفين")
    print("=" * 60)
    
    # إنشاء التطبيق
    app = create_test_app()
    
    print("✅ تم إعداد التطبيق بنجاح")
    print("🔧 اختبار شريط التبويبات...")
    print("🌐 الخادم متاح على: http://localhost:5000")
    print("📱 سيتم فتح المتصفح تلقائياً...")
    print("💡 للإيقاف: اضغط Ctrl+C")
    print("=" * 60)
    print("🎯 ما يجب أن تراه:")
    print("✅ رسالة الترحيب")
    print("✅ شريط التبويبات مع 10 تبويبات")
    print("✅ بطاقات الإحصائيات")
    print("✅ جميع التبويبات قابلة للنقر")
    print("=" * 60)
    
    # فتح المتصفح
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        app.run(debug=False, host='127.0.0.1', port=5000, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
    finally:
        print("👋 تم إغلاق النظام")

if __name__ == '__main__':
    main()
