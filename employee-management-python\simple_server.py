#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
خادم بسيط لنظام إدارة الموظفين
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import os
import json

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/templates/index.html'
        elif self.path.startswith('/api/'):
            self.handle_api_request()
            return
        elif self.path.startswith('/static/'):
            # إزالة /static من المسار
            self.path = self.path[7:]
        
        return http.server.SimpleHTTPRequestHandler.do_GET(self)
    
    def handle_api_request(self):
        """معالجة طلبات API"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/api/statistics':
            response = {
                'employees_count': 15,
                'courses_count': 8,
                'leaves_count': 3,
                'delegations_count': 2,
                'books_count': 25,
                'borrowed_books': 5,
                'unread_notifications': 1
            }
        elif self.path == '/api/employees':
            response = [
                {
                    'id': 1,
                    'sequence': 1,
                    'company_number': 'EMP001',
                    'full_name': 'أحمد محمد علي',
                    'job_title': 'مطور برمجيات',
                    'work_location': 'الرياض',
                    'start_date': '2023-01-15',
                    'address': 'الرياض - حي النرجس',
                    'birth_date': '1990-05-20',
                    'mobile': '0501234567'
                },
                {
                    'id': 2,
                    'sequence': 2,
                    'company_number': 'EMP002',
                    'full_name': 'فاطمة خالد السعد',
                    'job_title': 'محاسبة',
                    'work_location': 'جدة',
                    'start_date': '2023-02-01',
                    'address': 'جدة - حي الزهراء',
                    'birth_date': '1988-08-12',
                    'mobile': '0509876543'
                }
            ]
        elif self.path == '/api/service':
            response = [
                {
                    'id': 1,
                    'sequence': 1,
                    'company_number': 'EMP001',
                    'full_name': 'أحمد محمد علي',
                    'job_title': 'مطور برمجيات',
                    'service_duration': '1 سنة و 2 شهر و 15 يوم'
                }
            ]
        else:
            response = []
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:8000')
        print("🌐 تم فتح المتصفح على http://localhost:8000")
    except:
        print("⚠️ افتح المتصفح يدوياً على: http://localhost:8000")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🚀 خادم بسيط لنظام إدارة الموظفين")
    print("=" * 60)
    
    # تغيير المجلد إلى مجلد المشروع
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    PORT = 8000
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ الخادم يعمل على المنفذ {PORT}")
            print(f"🌐 افتح المتصفح على: http://localhost:{PORT}")
            print("💡 للإيقاف: اضغط Ctrl+C")
            print("=" * 60)
            print("🎯 ما يجب أن تراه:")
            print("✅ رسالة الترحيب")
            print("✅ شريط التبويبات مع 10 تبويبات")
            print("✅ بطاقات الإحصائيات")
            print("✅ جميع التبويبات قابلة للنقر")
            print("=" * 60)
            
            # فتح المتصفح
            browser_thread = threading.Thread(target=open_browser_delayed)
            browser_thread.daemon = True
            browser_thread.start()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        print("💡 جرب منفذ آخر أو تأكد من عدم استخدام المنفذ")

if __name__ == '__main__':
    main()
