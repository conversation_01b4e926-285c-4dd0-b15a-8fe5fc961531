from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import sqlite3
from datetime import datetime
import locale

# تعيين اللغة العربية
try:
    locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'ar_SA')
    except:
        pass

# إنشاء تطبيق Flask
app = Flask(__name__)
app.secret_key = 'employee_management_secret_key_2024'
CORS(app)

# تكوين قاعدة البيانات
DATABASE = 'employee_management.db'

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # إنشاء جدول الموظفين
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_number TEXT NOT NULL,
        full_name TEXT NOT NULL,
        job_title TEXT NOT NULL,
        work_location TEXT NOT NULL,
        start_date TEXT NOT NULL,
        address TEXT,
        birth_date TEXT,
        mobile TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
    )
    ''')

    # إنشاء جدول الخدمة
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS service (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        start_date TEXT NOT NULL,
        service_duration TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')

    # إنشاء جدول الدورات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS courses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        type TEXT NOT NULL,
        period TEXT NOT NULL,
        location TEXT NOT NULL,
        start_date TEXT,
        end_date TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')

    # إنشاء جدول الإجازات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS leaves (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        leave_type TEXT,
        leave_date TEXT NOT NULL,
        leave_duration TEXT NOT NULL,
        leave_reason TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')

    # إنشاء جدول الإيفادات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS delegations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        destination TEXT NOT NULL,
        country TEXT NOT NULL,
        purpose TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')

    # إنشاء جدول التنبيهات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL,
        date TEXT NOT NULL,
        read INTEGER DEFAULT 0,
        read_date TEXT,
        employee_id INTEGER,
        created_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')

    # إنشاء جدول أرشفة الكتب
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS books (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        author TEXT,
        isbn TEXT,
        category TEXT,
        publisher TEXT,
        publication_year INTEGER,
        pages INTEGER,
        language TEXT DEFAULT 'العربية',
        location TEXT,
        status TEXT DEFAULT 'متاح',
        description TEXT,
        borrowed_by TEXT,
        borrowed_date TEXT,
        return_date TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
    )
    ''')

    # إنشاء جدول الإعدادات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS settings (
        id TEXT PRIMARY KEY,
        theme TEXT DEFAULT 'light',
        font TEXT DEFAULT 'tajawal',
        font_size TEXT DEFAULT 'medium',
        theme_color TEXT DEFAULT 'default',
        rtl INTEGER DEFAULT 1,
        language TEXT DEFAULT 'ar',
        date_format TEXT DEFAULT 'gregorian',
        notifications_enabled INTEGER DEFAULT 1,
        notification_duration INTEGER DEFAULT 5000,
        employee_notifications INTEGER DEFAULT 1,
        leave_notifications INTEGER DEFAULT 1,
        course_notifications INTEGER DEFAULT 1,
        delegation_notifications INTEGER DEFAULT 1,
        system_notifications INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
    )
    ''')

    # إدخال إعدادات افتراضية
    cursor.execute("SELECT COUNT(*) FROM settings WHERE id = 'app_settings'")
    if cursor.fetchone()[0] == 0:
        cursor.execute('''
        INSERT INTO settings (
            id, theme, font, font_size, theme_color, rtl, language, date_format,
            notifications_enabled, notification_duration, employee_notifications,
            leave_notifications, course_notifications, delegation_notifications,
            system_notifications, created_at
        ) VALUES (
            'app_settings', 'light', 'tajawal', 'medium', 'default', 1, 'ar', 'gregorian',
            1, 5000, 1, 1, 1, 1, 1, ?
        )
        ''', (datetime.now().isoformat(),))

    conn.commit()
    conn.close()

def calculate_service_duration(start_date):
    """حساب مدة الخدمة"""
    try:
        start = datetime.fromisoformat(start_date.replace('Z', '+00:00') if 'Z' in start_date else start_date)
        now = datetime.now()

        delta = now - start
        total_days = delta.days

        years = total_days // 365
        remaining_days = total_days % 365
        months = remaining_days // 30
        days = remaining_days % 30

        duration_text = ''

        if years > 0:
            if years == 1:
                duration_text += '1 سنة'
            elif years == 2:
                duration_text += 'سنتان'
            elif years >= 3 and years <= 10:
                duration_text += f'{years} سنوات'
            else:
                duration_text += f'{years} سنة'

        if months > 0:
            if duration_text:
                duration_text += ' و '

            if months == 1:
                duration_text += '1 شهر'
            elif months == 2:
                duration_text += 'شهران'
            elif months >= 3 and months <= 10:
                duration_text += f'{months} أشهر'
            else:
                duration_text += f'{months} شهر'

        if days > 0 or (years == 0 and months == 0):
            if duration_text:
                duration_text += ' و '

            if days == 1:
                duration_text += '1 يوم'
            elif days == 2:
                duration_text += 'يومان'
            elif days >= 3 and days <= 10:
                duration_text += f'{days} أيام'
            else:
                duration_text += f'{days} يوم'

        return duration_text
    except Exception as e:
        print(f"خطأ في حساب مدة الخدمة: {e}")
        return "غير معروف"

def add_sample_data():
    """إضافة بيانات تجريبية"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # التحقق من وجود بيانات
    cursor.execute("SELECT COUNT(*) FROM employees")
    if cursor.fetchone()[0] > 0:
        conn.close()
        return

    # إضافة موظفين تجريبيين
    employees = [
        {
            'company_number': '1001',
            'full_name': 'أحمد محمد علي',
            'job_title': 'مهندس برمجيات',
            'work_location': 'الرياض',
            'start_date': '2020-01-15',
            'address': 'الرياض، حي النزهة',
            'birth_date': '1990-05-20',
            'mobile': '0501234567'
        },
        {
            'company_number': '1002',
            'full_name': 'محمد عبدالله الأحمد',
            'job_title': 'محلل نظم',
            'work_location': 'جدة',
            'start_date': '2019-03-10',
            'address': 'جدة، حي الروضة',
            'birth_date': '1988-11-15',
            'mobile': '0567891234'
        },
        {
            'company_number': '1003',
            'full_name': 'سارة خالد العتيبي',
            'job_title': 'مصممة واجهات',
            'work_location': 'الرياض',
            'start_date': '2021-06-01',
            'address': 'الرياض، حي الملقا',
            'birth_date': '1995-08-25',
            'mobile': '0512345678'
        }
    ]

    created_at = datetime.now().isoformat()

    for employee in employees:
        cursor.execute('''
        INSERT INTO employees (
            company_number, full_name, job_title, work_location, start_date,
            address, birth_date, mobile, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            employee['company_number'], employee['full_name'], employee['job_title'],
            employee['work_location'], employee['start_date'], employee['address'],
            employee['birth_date'], employee['mobile'], created_at
        ))

        employee_id = cursor.lastrowid

        # إضافة بيانات الخدمة
        service_duration = calculate_service_duration(employee['start_date'])
        cursor.execute('''
        INSERT INTO service (employee_id, start_date, service_duration, created_at)
        VALUES (?, ?, ?, ?)
        ''', (employee_id, employee['start_date'], service_duration, created_at))

    conn.commit()
    conn.close()

# تهيئة قاعدة البيانات
init_db()
add_sample_data()

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/favicon.ico')
def favicon():
    """أيقونة الموقع"""
    return app.send_static_file('favicon.ico')

# API للموظفين
@app.route('/api/employees', methods=['GET'])
def get_employees():
    """جلب جميع الموظفين"""
    conn = get_db_connection()
    employees = conn.execute('SELECT * FROM employees ORDER BY id DESC').fetchall()
    conn.close()

    employees_list = []
    for i, emp in enumerate(employees, 1):
        employees_list.append({
            'sequence': i,
            'id': emp['id'],
            'company_number': emp['company_number'],
            'full_name': emp['full_name'],
            'job_title': emp['job_title'],
            'work_location': emp['work_location'],
            'start_date': emp['start_date'],
            'address': emp['address'],
            'birth_date': emp['birth_date'],
            'mobile': emp['mobile']
        })

    return jsonify(employees_list)

@app.route('/api/employees', methods=['POST'])
def add_employee():
    """إضافة موظف جديد"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    created_at = datetime.now().isoformat()

    cursor.execute('''
    INSERT INTO employees (
        company_number, full_name, job_title, work_location, start_date,
        address, birth_date, mobile, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        data['company_number'], data['full_name'], data['job_title'],
        data['work_location'], data['start_date'], data['address'],
        data['birth_date'], data['mobile'], created_at
    ))

    employee_id = cursor.lastrowid

    # إضافة بيانات الخدمة
    service_duration = calculate_service_duration(data['start_date'])
    cursor.execute('''
    INSERT INTO service (employee_id, start_date, service_duration, created_at)
    VALUES (?, ?, ?, ?)
    ''', (employee_id, data['start_date'], service_duration, created_at))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم إضافة الموظف بنجاح'})

@app.route('/api/employees/<int:employee_id>', methods=['PUT'])
def update_employee(employee_id):
    """تحديث بيانات موظف"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    updated_at = datetime.now().isoformat()

    cursor.execute('''
    UPDATE employees SET
        company_number = ?, full_name = ?, job_title = ?, work_location = ?,
        start_date = ?, address = ?, birth_date = ?, mobile = ?, updated_at = ?
    WHERE id = ?
    ''', (
        data['company_number'], data['full_name'], data['job_title'],
        data['work_location'], data['start_date'], data['address'],
        data['birth_date'], data['mobile'], updated_at, employee_id
    ))

    # تحديث بيانات الخدمة
    service_duration = calculate_service_duration(data['start_date'])
    cursor.execute('''
    UPDATE service SET start_date = ?, service_duration = ?, updated_at = ?
    WHERE employee_id = ?
    ''', (data['start_date'], service_duration, updated_at, employee_id))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث بيانات الموظف بنجاح'})

@app.route('/api/employees/<int:employee_id>', methods=['DELETE'])
def delete_employee(employee_id):
    """حذف موظف"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # حذف البيانات المرتبطة
    cursor.execute('DELETE FROM service WHERE employee_id = ?', (employee_id,))
    cursor.execute('DELETE FROM courses WHERE employee_id = ?', (employee_id,))
    cursor.execute('DELETE FROM leaves WHERE employee_id = ?', (employee_id,))
    cursor.execute('DELETE FROM delegations WHERE employee_id = ?', (employee_id,))

    # حذف الموظف
    cursor.execute('DELETE FROM employees WHERE id = ?', (employee_id,))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم حذف الموظف بنجاح'})

# API للخدمة
@app.route('/api/service', methods=['GET'])
def get_service():
    """جلب بيانات الخدمة"""
    conn = get_db_connection()
    service_data = conn.execute('''
    SELECT s.*, e.company_number, e.full_name, e.job_title
    FROM service s
    JOIN employees e ON s.employee_id = e.id
    ORDER BY s.id DESC
    ''').fetchall()
    conn.close()

    service_list = []
    for i, service in enumerate(service_data, 1):
        service_list.append({
            'sequence': i,
            'id': service['id'],
            'company_number': service['company_number'],
            'full_name': service['full_name'],
            'job_title': service['job_title'],
            'service_duration': service['service_duration']
        })

    return jsonify(service_list)

# API للدورات
@app.route('/api/courses', methods=['GET'])
def get_courses():
    """جلب جميع الدورات"""
    conn = get_db_connection()
    courses = conn.execute('''
    SELECT c.*, e.company_number, e.full_name, e.job_title
    FROM courses c
    JOIN employees e ON c.employee_id = e.id
    ORDER BY c.id DESC
    ''').fetchall()
    conn.close()

    courses_list = []
    for i, course in enumerate(courses, 1):
        courses_list.append({
            'sequence': i,
            'id': course['id'],
            'company_number': course['company_number'],
            'full_name': course['full_name'],
            'job_title': course['job_title'],
            'title': course['title'],
            'type': course['type'],
            'period': course['period'],
            'location': course['location'],
            'start_date': course['start_date'],
            'end_date': course['end_date']
        })

    return jsonify(courses_list)

@app.route('/api/courses', methods=['POST'])
def add_course():
    """إضافة دورة جديدة"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    created_at = datetime.now().isoformat()

    cursor.execute('''
    INSERT INTO courses (
        employee_id, title, type, period, location, start_date, end_date, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        data['employee_id'], data['title'], data['type'], data['period'],
        data['location'], data.get('start_date'), data.get('end_date'), created_at
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم إضافة الدورة بنجاح'})

@app.route('/api/courses/<int:course_id>', methods=['PUT'])
def update_course(course_id):
    """تحديث دورة"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    updated_at = datetime.now().isoformat()

    cursor.execute('''
    UPDATE courses SET
        employee_id = ?, title = ?, type = ?, period = ?, location = ?,
        start_date = ?, end_date = ?, updated_at = ?
    WHERE id = ?
    ''', (
        data['employee_id'], data['title'], data['type'], data['period'],
        data['location'], data.get('start_date'), data.get('end_date'),
        updated_at, course_id
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث الدورة بنجاح'})

@app.route('/api/courses/<int:course_id>', methods=['DELETE'])
def delete_course(course_id):
    """حذف دورة"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('DELETE FROM courses WHERE id = ?', (course_id,))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم حذف الدورة بنجاح'})

# API للإجازات
@app.route('/api/leaves', methods=['GET'])
def get_leaves():
    """جلب جميع الإجازات"""
    conn = get_db_connection()
    leaves = conn.execute('''
    SELECT l.*, e.company_number, e.full_name, e.job_title, e.work_location
    FROM leaves l
    JOIN employees e ON l.employee_id = e.id
    ORDER BY l.id DESC
    ''').fetchall()
    conn.close()

    leaves_list = []
    for i, leave in enumerate(leaves, 1):
        leaves_list.append({
            'sequence': i,
            'id': leave['id'],
            'company_number': leave['company_number'],
            'full_name': leave['full_name'],
            'job_title': leave['job_title'],
            'work_location': leave['work_location'],
            'leave_type': leave['leave_type'],
            'leave_date': leave['leave_date'],
            'leave_duration': leave['leave_duration'],
            'leave_reason': leave['leave_reason']
        })

    return jsonify(leaves_list)

@app.route('/api/leaves', methods=['POST'])
def add_leave():
    """إضافة إجازة جديدة"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    created_at = datetime.now().isoformat()

    cursor.execute('''
    INSERT INTO leaves (
        employee_id, leave_type, leave_date, leave_duration, leave_reason, created_at
    ) VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        data['employee_id'], data.get('leave_type'), data['leave_date'],
        data['leave_duration'], data.get('leave_reason'), created_at
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم إضافة الإجازة بنجاح'})

@app.route('/api/leaves/<int:leave_id>', methods=['PUT'])
def update_leave(leave_id):
    """تحديث إجازة"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    updated_at = datetime.now().isoformat()

    cursor.execute('''
    UPDATE leaves SET
        employee_id = ?, leave_type = ?, leave_date = ?, leave_duration = ?,
        leave_reason = ?, updated_at = ?
    WHERE id = ?
    ''', (
        data['employee_id'], data.get('leave_type'), data['leave_date'],
        data['leave_duration'], data.get('leave_reason'), updated_at, leave_id
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث الإجازة بنجاح'})

@app.route('/api/leaves/<int:leave_id>', methods=['DELETE'])
def delete_leave(leave_id):
    """حذف إجازة"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('DELETE FROM leaves WHERE id = ?', (leave_id,))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم حذف الإجازة بنجاح'})

# API للإيفادات
@app.route('/api/delegations', methods=['GET'])
def get_delegations():
    """جلب جميع الإيفادات"""
    conn = get_db_connection()
    delegations = conn.execute('''
    SELECT d.*, e.company_number, e.full_name, e.job_title
    FROM delegations d
    JOIN employees e ON d.employee_id = e.id
    ORDER BY d.id DESC
    ''').fetchall()
    conn.close()

    delegations_list = []
    for i, delegation in enumerate(delegations, 1):
        delegations_list.append({
            'sequence': i,
            'id': delegation['id'],
            'company_number': delegation['company_number'],
            'full_name': delegation['full_name'],
            'job_title': delegation['job_title'],
            'destination': delegation['destination'],
            'country': delegation['country'],
            'purpose': delegation['purpose'],
            'start_date': delegation['start_date'],
            'end_date': delegation['end_date']
        })

    return jsonify(delegations_list)

@app.route('/api/delegations', methods=['POST'])
def add_delegation():
    """إضافة إيفاد جديد"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    created_at = datetime.now().isoformat()

    cursor.execute('''
    INSERT INTO delegations (
        employee_id, destination, country, purpose, start_date, end_date, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (
        data['employee_id'], data['destination'], data['country'],
        data['purpose'], data['start_date'], data['end_date'], created_at
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم إضافة الإيفاد بنجاح'})

@app.route('/api/delegations/<int:delegation_id>', methods=['DELETE'])
def delete_delegation(delegation_id):
    """حذف إيفاد"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('DELETE FROM delegations WHERE id = ?', (delegation_id,))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم حذف الإيفاد بنجاح'})

# API للتنبيهات
@app.route('/api/notifications', methods=['GET'])
def get_notifications():
    """جلب جميع التنبيهات"""
    conn = get_db_connection()
    notifications = conn.execute('''
    SELECT n.*, e.full_name as employee_name
    FROM notifications n
    LEFT JOIN employees e ON n.employee_id = e.id
    ORDER BY n.id DESC
    ''').fetchall()
    conn.close()

    notifications_list = []
    for i, notification in enumerate(notifications, 1):
        notifications_list.append({
            'sequence': i,
            'id': notification['id'],
            'title': notification['title'],
            'message': notification['message'],
            'type': notification['type'],
            'date': notification['date'],
            'read': notification['read'],
            'employee_name': notification['employee_name']
        })

    return jsonify(notifications_list)

@app.route('/api/notifications', methods=['POST'])
def add_notification():
    """إضافة تنبيه جديد"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    created_at = datetime.now().isoformat()

    cursor.execute('''
    INSERT INTO notifications (
        title, message, type, date, employee_id, created_at
    ) VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        data['title'], data['message'], data['type'],
        data['date'], data.get('employee_id'), created_at
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم إضافة التنبيه بنجاح'})

@app.route('/api/notifications/<int:notification_id>/read', methods=['PUT'])
def mark_notification_read(notification_id):
    """تحديد التنبيه كمقروء"""
    conn = get_db_connection()
    cursor = conn.cursor()

    read_date = datetime.now().isoformat()

    cursor.execute('''
    UPDATE notifications SET read = 1, read_date = ?
    WHERE id = ?
    ''', (read_date, notification_id))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث التنبيه'})

# API للإعدادات
@app.route('/api/settings', methods=['GET'])
def get_settings():
    """جلب الإعدادات"""
    conn = get_db_connection()
    settings = conn.execute("SELECT * FROM settings WHERE id = 'app_settings'").fetchone()
    conn.close()

    if settings:
        return jsonify(dict(settings))
    else:
        return jsonify({})

@app.route('/api/settings', methods=['PUT'])
def update_settings():
    """تحديث الإعدادات"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    updated_at = datetime.now().isoformat()

    cursor.execute('''
    UPDATE settings SET
        theme = ?, font = ?, font_size = ?, theme_color = ?, rtl = ?,
        language = ?, date_format = ?, notifications_enabled = ?,
        notification_duration = ?, employee_notifications = ?,
        leave_notifications = ?, course_notifications = ?,
        delegation_notifications = ?, system_notifications = ?, updated_at = ?
    WHERE id = 'app_settings'
    ''', (
        data.get('theme', 'light'), data.get('font', 'tajawal'),
        data.get('font_size', 'medium'), data.get('theme_color', 'default'),
        data.get('rtl', 1), data.get('language', 'ar'),
        data.get('date_format', 'gregorian'), data.get('notifications_enabled', 1),
        data.get('notification_duration', 5000), data.get('employee_notifications', 1),
        data.get('leave_notifications', 1), data.get('course_notifications', 1),
        data.get('delegation_notifications', 1), data.get('system_notifications', 1),
        updated_at
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث الإعدادات بنجاح'})

# API للكتب
@app.route('/api/books', methods=['GET'])
def get_books():
    """جلب جميع الكتب"""
    conn = get_db_connection()
    books = conn.execute('SELECT * FROM books ORDER BY id DESC').fetchall()
    conn.close()

    books_list = []
    for i, book in enumerate(books, 1):
        books_list.append({
            'sequence': i,
            'id': book['id'],
            'title': book['title'],
            'author': book['author'],
            'isbn': book['isbn'],
            'category': book['category'],
            'publisher': book['publisher'],
            'publication_year': book['publication_year'],
            'pages': book['pages'],
            'language': book['language'],
            'location': book['location'],
            'status': book['status'],
            'description': book['description'],
            'borrowed_by': book['borrowed_by'],
            'borrowed_date': book['borrowed_date'],
            'return_date': book['return_date']
        })

    return jsonify(books_list)

@app.route('/api/books', methods=['POST'])
def add_book():
    """إضافة كتاب جديد"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    created_at = datetime.now().isoformat()

    cursor.execute('''
    INSERT INTO books (
        title, author, isbn, category, publisher, publication_year, pages,
        language, location, status, description, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        data['title'], data.get('author'), data.get('isbn'), data.get('category'),
        data.get('publisher'), data.get('publication_year'), data.get('pages'),
        data.get('language', 'العربية'), data.get('location'),
        data.get('status', 'متاح'), data.get('description'), created_at
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم إضافة الكتاب بنجاح'})

@app.route('/api/books/<int:book_id>', methods=['PUT'])
def update_book(book_id):
    """تحديث بيانات كتاب"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    updated_at = datetime.now().isoformat()

    cursor.execute('''
    UPDATE books SET
        title = ?, author = ?, isbn = ?, category = ?, publisher = ?,
        publication_year = ?, pages = ?, language = ?, location = ?,
        status = ?, description = ?, updated_at = ?
    WHERE id = ?
    ''', (
        data['title'], data.get('author'), data.get('isbn'), data.get('category'),
        data.get('publisher'), data.get('publication_year'), data.get('pages'),
        data.get('language', 'العربية'), data.get('location'),
        data.get('status', 'متاح'), data.get('description'), updated_at, book_id
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم تحديث بيانات الكتاب بنجاح'})

@app.route('/api/books/<int:book_id>', methods=['DELETE'])
def delete_book(book_id):
    """حذف كتاب"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('DELETE FROM books WHERE id = ?', (book_id,))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم حذف الكتاب بنجاح'})

@app.route('/api/books/<int:book_id>/borrow', methods=['PUT'])
def borrow_book(book_id):
    """استعارة كتاب"""
    data = request.get_json()

    conn = get_db_connection()
    cursor = conn.cursor()

    updated_at = datetime.now().isoformat()
    borrowed_date = datetime.now().isoformat()

    cursor.execute('''
    UPDATE books SET
        status = 'مُستعار', borrowed_by = ?, borrowed_date = ?,
        return_date = ?, updated_at = ?
    WHERE id = ?
    ''', (
        data['borrowed_by'], borrowed_date, data.get('return_date'),
        updated_at, book_id
    ))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم استعارة الكتاب بنجاح'})

@app.route('/api/books/<int:book_id>/return', methods=['PUT'])
def return_book(book_id):
    """إرجاع كتاب"""
    conn = get_db_connection()
    cursor = conn.cursor()

    updated_at = datetime.now().isoformat()

    cursor.execute('''
    UPDATE books SET
        status = 'متاح', borrowed_by = NULL, borrowed_date = NULL,
        return_date = NULL, updated_at = ?
    WHERE id = ?
    ''', (updated_at, book_id))

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': 'تم إرجاع الكتاب بنجاح'})

# API للتقارير والإحصائيات
@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """جلب الإحصائيات"""
    conn = get_db_connection()

    # عدد الموظفين
    employees_count = conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0]

    # عدد الدورات
    courses_count = conn.execute('SELECT COUNT(*) FROM courses').fetchone()[0]

    # عدد الإجازات
    leaves_count = conn.execute('SELECT COUNT(*) FROM leaves').fetchone()[0]

    # عدد الإيفادات
    delegations_count = conn.execute('SELECT COUNT(*) FROM delegations').fetchone()[0]

    # عدد التنبيهات غير المقروءة
    unread_notifications = conn.execute('SELECT COUNT(*) FROM notifications WHERE read = 0').fetchone()[0]

    # عدد الكتب
    books_count = conn.execute('SELECT COUNT(*) FROM books').fetchone()[0]

    # عدد الكتب المستعارة
    borrowed_books = conn.execute("SELECT COUNT(*) FROM books WHERE status = 'مُستعار'").fetchone()[0]

    conn.close()

    return jsonify({
        'employees_count': employees_count,
        'courses_count': courses_count,
        'leaves_count': leaves_count,
        'delegations_count': delegations_count,
        'books_count': books_count,
        'borrowed_books': borrowed_books,
        'unread_notifications': unread_notifications
    })

if __name__ == '__main__':
    app.run(debug=True, port=5000)
