from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import sqlite3
from datetime import datetime
import locale

# تعيين اللغة العربية
try:
    locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'ar_SA')
    except:
        pass

# إنشاء تطبيق Flask
app = Flask(__name__)
CORS(app)

# تكوين قاعدة البيانات
DATABASE = 'employee_management.db'

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إنشاء جدول الموظفين
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_number TEXT NOT NULL,
        full_name TEXT NOT NULL,
        job_title TEXT NOT NULL,
        work_location TEXT NOT NULL,
        start_date TEXT NOT NULL,
        address TEXT,
        birth_date TEXT,
        mobile TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
    )
    ''')
    
    # إنشاء جدول الخدمة
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS service (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        start_date TEXT NOT NULL,
        service_duration TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')
    
    # إنشاء جدول الدورات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS courses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        type TEXT NOT NULL,
        period TEXT NOT NULL,
        location TEXT NOT NULL,
        start_date TEXT,
        end_date TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')
    
    # إنشاء جدول الإجازات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS leaves (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        leave_type TEXT,
        leave_date TEXT NOT NULL,
        leave_duration TEXT NOT NULL,
        leave_reason TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')
    
    # إنشاء جدول الإيفادات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS delegations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        destination TEXT NOT NULL,
        country TEXT NOT NULL,
        purpose TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')
    
    # إنشاء جدول التنبيهات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL,
        date TEXT NOT NULL,
        read INTEGER DEFAULT 0,
        read_date TEXT,
        employee_id INTEGER,
        created_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id)
    )
    ''')
    
    # إنشاء جدول الإعدادات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS settings (
        id TEXT PRIMARY KEY,
        theme TEXT DEFAULT 'light',
        font TEXT DEFAULT 'tajawal',
        font_size TEXT DEFAULT 'medium',
        theme_color TEXT DEFAULT 'default',
        rtl INTEGER DEFAULT 1,
        language TEXT DEFAULT 'ar',
        date_format TEXT DEFAULT 'gregorian',
        notifications_enabled INTEGER DEFAULT 1,
        notification_duration INTEGER DEFAULT 5000,
        employee_notifications INTEGER DEFAULT 1,
        leave_notifications INTEGER DEFAULT 1,
        course_notifications INTEGER DEFAULT 1,
        delegation_notifications INTEGER DEFAULT 1,
        system_notifications INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
    )
    ''')
    
    # إدخال إعدادات افتراضية
    cursor.execute("SELECT COUNT(*) FROM settings WHERE id = 'app_settings'")
    if cursor.fetchone()[0] == 0:
        cursor.execute('''
        INSERT INTO settings (
            id, theme, font, font_size, theme_color, rtl, language, date_format,
            notifications_enabled, notification_duration, employee_notifications,
            leave_notifications, course_notifications, delegation_notifications,
            system_notifications, created_at
        ) VALUES (
            'app_settings', 'light', 'tajawal', 'medium', 'default', 1, 'ar', 'gregorian',
            1, 5000, 1, 1, 1, 1, 1, ?
        )
        ''', (datetime.now().isoformat(),))
    
    conn.commit()
    conn.close()

def calculate_service_duration(start_date):
    """حساب مدة الخدمة"""
    try:
        start = datetime.fromisoformat(start_date.replace('Z', '+00:00') if 'Z' in start_date else start_date)
        now = datetime.now()
        
        delta = now - start
        total_days = delta.days
        
        years = total_days // 365
        remaining_days = total_days % 365
        months = remaining_days // 30
        days = remaining_days % 30
        
        duration_text = ''
        
        if years > 0:
            if years == 1:
                duration_text += '1 سنة'
            elif years == 2:
                duration_text += 'سنتان'
            elif years >= 3 and years <= 10:
                duration_text += f'{years} سنوات'
            else:
                duration_text += f'{years} سنة'
        
        if months > 0:
            if duration_text:
                duration_text += ' و '
            
            if months == 1:
                duration_text += '1 شهر'
            elif months == 2:
                duration_text += 'شهران'
            elif months >= 3 and months <= 10:
                duration_text += f'{months} أشهر'
            else:
                duration_text += f'{months} شهر'
        
        if days > 0 or (years == 0 and months == 0):
            if duration_text:
                duration_text += ' و '
            
            if days == 1:
                duration_text += '1 يوم'
            elif days == 2:
                duration_text += 'يومان'
            elif days >= 3 and days <= 10:
                duration_text += f'{days} أيام'
            else:
                duration_text += f'{days} يوم'
        
        return duration_text
    except Exception as e:
        print(f"خطأ في حساب مدة الخدمة: {e}")
        return "غير معروف"

def add_sample_data():
    """إضافة بيانات تجريبية"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # التحقق من وجود بيانات
    cursor.execute("SELECT COUNT(*) FROM employees")
    if cursor.fetchone()[0] > 0:
        conn.close()
        return
    
    # إضافة موظفين تجريبيين
    employees = [
        {
            'company_number': '1001',
            'full_name': 'أحمد محمد علي',
            'job_title': 'مهندس برمجيات',
            'work_location': 'الرياض',
            'start_date': '2020-01-15',
            'address': 'الرياض، حي النزهة',
            'birth_date': '1990-05-20',
            'mobile': '0501234567'
        },
        {
            'company_number': '1002',
            'full_name': 'محمد عبدالله الأحمد',
            'job_title': 'محلل نظم',
            'work_location': 'جدة',
            'start_date': '2019-03-10',
            'address': 'جدة، حي الروضة',
            'birth_date': '1988-11-15',
            'mobile': '0567891234'
        },
        {
            'company_number': '1003',
            'full_name': 'سارة خالد العتيبي',
            'job_title': 'مصممة واجهات',
            'work_location': 'الرياض',
            'start_date': '2021-06-01',
            'address': 'الرياض، حي الملقا',
            'birth_date': '1995-08-25',
            'mobile': '0512345678'
        }
    ]
    
    created_at = datetime.now().isoformat()
    
    for employee in employees:
        cursor.execute('''
        INSERT INTO employees (
            company_number, full_name, job_title, work_location, start_date,
            address, birth_date, mobile, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            employee['company_number'], employee['full_name'], employee['job_title'],
            employee['work_location'], employee['start_date'], employee['address'],
            employee['birth_date'], employee['mobile'], created_at
        ))
        
        employee_id = cursor.lastrowid
        
        # إضافة بيانات الخدمة
        service_duration = calculate_service_duration(employee['start_date'])
        cursor.execute('''
        INSERT INTO service (employee_id, start_date, service_duration, created_at)
        VALUES (?, ?, ?, ?)
        ''', (employee_id, employee['start_date'], service_duration, created_at))
    
    conn.commit()
    conn.close()

# تهيئة قاعدة البيانات
init_db()
add_sample_data()

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

if __name__ == '__main__':
    app.run(debug=True, port=5000)
