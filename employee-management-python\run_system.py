#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين - ملف التشغيل السريع
Employee Management System - Quick Run File
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    print("🔄 تثبيت المكتبات المطلوبة...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المكتبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المكتبات: {e}")
        return False

def check_database():
    """التحقق من قاعدة البيانات وإنشاؤها إذا لم تكن موجودة"""
    print("🔄 التحقق من قاعدة البيانات...")
    try:
        from database import EmployeeDatabase
        db = EmployeeDatabase()
        print("✅ قاعدة البيانات جاهزة")
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def open_browser():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)  # انتظار 3 ثوان لبدء الخادم
    webbrowser.open('http://localhost:5000')

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🚀 نظام إدارة الموظفين - التشغيل السريع")
    print("=" * 60)
    
    # التحقق من وجود Python
    print(f"🐍 إصدار Python: {sys.version}")
    
    # تثبيت المكتبات
    if not install_requirements():
        print("❌ فشل في تثبيت المكتبات. يرجى تثبيتها يدوياً:")
        print("pip install Flask Flask-CORS")
        return
    
    # التحقق من قاعدة البيانات
    if not check_database():
        print("❌ فشل في إعداد قاعدة البيانات")
        return
    
    print("\n" + "=" * 60)
    print("🌐 بدء تشغيل الخادم...")
    print("📊 قاعدة البيانات: employee_system.db")
    print("🔗 الرابط: http://localhost:5000")
    print("=" * 60)
    print("💡 نصائح:")
    print("   - سيتم فتح المتصفح تلقائياً")
    print("   - لإيقاف الخادم: اضغط Ctrl+C")
    print("   - البيانات محفوظة في قاعدة البيانات")
    print("=" * 60)
    
    # فتح المتصفح بعد تأخير
    Timer(3.0, open_browser).start()
    
    # تشغيل الخادم
    try:
        from server import app
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        print("\n🔧 حلول مقترحة:")
        print("1. تأكد من عدم تشغيل خادم آخر على المنفذ 5000")
        print("2. جرب تشغيل الأمر: python server.py")
        print("3. تحقق من تثبيت Flask: pip install Flask Flask-CORS")

if __name__ == "__main__":
    main()
