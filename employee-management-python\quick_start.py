#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إدارة الموظفين - تشغيل سريع
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def quick_start():
    """تشغيل سريع للنظام"""
    print("🚀 تشغيل سريع لنظام إدارة الموظفين")
    print("=" * 50)
    
    try:
        # استيراد التطبيق
        from app import app
        
        print("✅ تم تحميل النظام بنجاح")
        print("🌐 الخادم متاح على: http://localhost:5000")
        print("🔧 تم إصلاح مشكلة اختفاء رسالة الترحيب والبطاقات")
        print("=" * 50)
        
        # تشغيل الخادم
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("💡 تأكد من تثبيت Flask: pip install flask flask-cors")

if __name__ == '__main__':
    quick_start()
