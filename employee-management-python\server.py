#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين - خادم Flask
Employee Management System - Flask Server
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import json
from database import EmployeeDatabase

# إنشاء تطبيق Flask
app = Flask(__name__)
CORS(app)  # للسماح بطلبات AJAX من المتصفح

# إنشاء مثيل من قاعدة البيانات
db = EmployeeDatabase()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return send_from_directory('.', 'working_system.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """تقديم الملفات الثابتة"""
    return send_from_directory('.', filename)

# ==================== API للموظفين ====================

@app.route('/api/employees', methods=['GET'])
def get_employees():
    """جلب جميع الموظفين"""
    try:
        employees = db.get_all_employees()
        return jsonify({
            'success': True,
            'data': employees,
            'count': len(employees)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/employees', methods=['POST'])
def add_employee():
    """إضافة موظف جديد"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات'
            }), 400
        
        employee_id = db.add_employee(data)
        
        if employee_id:
            return jsonify({
                'success': True,
                'message': 'تم إضافة الموظف بنجاح',
                'employee_id': employee_id
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في إضافة الموظف'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/employees/<int:employee_id>', methods=['PUT'])
def update_employee(employee_id):
    """تحديث بيانات موظف"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات'
            }), 400
        
        success = db.update_employee(employee_id, data)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'تم تحديث بيانات الموظف بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في تحديث بيانات الموظف'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/employees/<int:employee_id>', methods=['DELETE'])
def delete_employee(employee_id):
    """حذف موظف"""
    try:
        success = db.delete_employee(employee_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'تم حذف الموظف بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في حذف الموظف'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== API للدورات ====================

@app.route('/api/courses', methods=['GET'])
def get_courses():
    """جلب جميع الدورات"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM courses ORDER BY id DESC')
        courses = cursor.fetchall()
        conn.close()
        
        result = []
        for i, course in enumerate(courses, 1):
            result.append({
                'id': course['id'],
                'sequence': i,
                'employeeId': course['employee_id'],
                'employeeName': course['employee_name'] or 'غير محدد',
                'employeeNumber': course['employee_number'] or 'غير محدد',
                'employeeJobTitle': course['employee_job_title'] or 'غير محدد',
                'courseName': course['course_name'] or 'غير محدد',
                'courseType': course['course_type'] or 'غير محدد',
                'duration': course['duration'] or 'غير محدد',
                'startDate': course['start_date'] or '',
                'endDate': course['end_date'] or '',
                'location': course['location'] or 'غير محدد',
                'status': course['status'] or 'مخططة',
                'notes': course['notes'] or '-'
            })
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/courses', methods=['POST'])
def add_course():
    """إضافة دورة جديدة"""
    try:
        data = request.get_json()
        
        conn = db.get_connection()
        cursor = conn.cursor()
        
        from datetime import datetime
        created_at = datetime.now().isoformat()
        
        cursor.execute('''
            INSERT INTO courses (
                employee_id, employee_name, employee_number, employee_job_title,
                course_name, course_type, duration, start_date, end_date,
                location, status, notes, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('employeeId', ''),
            data.get('employeeName', 'غير محدد'),
            data.get('employeeNumber', 'غير محدد'),
            data.get('employeeJobTitle', 'غير محدد'),
            data.get('courseName', 'دورة جديدة'),
            data.get('courseType', 'غير محدد'),
            data.get('duration', 'غير محدد'),
            data.get('startDate', ''),
            data.get('endDate', ''),
            data.get('location', 'غير محدد'),
            data.get('status', 'مخططة'),
            data.get('notes', '-'),
            created_at
        ))
        
        course_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'تم إضافة الدورة بنجاح',
            'course_id': course_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== API للإجازات ====================

@app.route('/api/leaves', methods=['POST'])
def add_leave():
    """إضافة إجازة جديدة"""
    try:
        data = request.get_json()
        
        conn = db.get_connection()
        cursor = conn.cursor()
        
        from datetime import datetime
        created_at = datetime.now().isoformat()
        
        cursor.execute('''
            INSERT INTO leaves (
                employee_id, employee_name, employee_number, employee_job_title,
                employee_work_location, leave_type, start_date, end_date,
                days, status, reason, notes, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('employeeId', ''),
            data.get('employeeName', 'غير محدد'),
            data.get('employeeNumber', 'غير محدد'),
            data.get('employeeJobTitle', 'غير محدد'),
            data.get('employeeWorkLocation', 'غير محدد'),
            data.get('leaveType', 'غير محدد'),
            data.get('startDate', ''),
            data.get('endDate', ''),
            data.get('days', 0),
            data.get('status', 'معلقة'),
            data.get('reason', 'غير محدد'),
            data.get('notes', '-'),
            created_at
        ))
        
        leave_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'تم إضافة الإجازة بنجاح',
            'leave_id': leave_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== API للكتب ====================

@app.route('/api/books', methods=['POST'])
def add_book():
    """إضافة كتاب جديد"""
    try:
        data = request.get_json()
        
        conn = db.get_connection()
        cursor = conn.cursor()
        
        from datetime import datetime
        created_at = datetime.now().isoformat()
        
        cursor.execute('''
            INSERT INTO books (
                number, date, subject, source, title,
                folder, attachments, notes, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('number', 'غير محدد'),
            data.get('date', datetime.now().strftime('%Y-%m-%d')),
            data.get('subject', 'موضوع جديد'),
            data.get('source', 'غير محدد'),
            data.get('title', 'كتاب جديد'),
            data.get('folder', '-'),
            data.get('attachments', 'بدون مرفقات'),
            data.get('notes', '-'),
            created_at
        ))
        
        book_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'تم إضافة الكتاب بنجاح',
            'book_id': book_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== تشغيل الخادم ====================

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم نظام إدارة الموظفين...")
    print("📊 قاعدة البيانات: employee_system.db")
    print("🌐 الرابط: http://localhost:5000")
    print("=" * 50)
    
    # التأكد من وجود قاعدة البيانات
    if os.path.exists('employee_system.db'):
        print("✅ قاعدة البيانات موجودة")
    else:
        print("🔄 إنشاء قاعدة بيانات جديدة...")
    
    # تشغيل الخادم
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False
    )
