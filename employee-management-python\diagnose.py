#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشخيص شامل لمشاكل النظام
"""

import os
import sys
import subprocess
import socket
import sqlite3

def check_python():
    """فحص Python"""
    print("🐍 فحص Python:")
    print(f"   الإصدار: {sys.version}")
    print(f"   المسار: {sys.executable}")
    print(f"   مجلد العمل: {os.getcwd()}")

    if sys.version_info < (3, 6):
        print("   ❌ يتطلب Python 3.6 أو أحدث")
        return False
    else:
        print("   ✅ إصدار Python مناسب")
        return True

def check_pip():
    """فحص pip"""
    print("\n📦 فحص pip:")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ pip متاح: {result.stdout.strip()}")
            return True
        else:
            print("   ❌ pip غير متاح")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في فحص pip: {e}")
        return False

def check_packages():
    """فحص المكتبات المطلوبة"""
    print("\n📚 فحص المكتبات:")
    packages = {
        'flask': 'Flask',
        'flask_cors': 'Flask-CORS',
        'sqlite3': 'SQLite3 (مدمج)',
        'json': 'JSON (مدمج)',
        'datetime': 'DateTime (مدمج)'
    }

    missing = []
    for package, name in packages.items():
        try:
            __import__(package)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name}")
            if package not in ['sqlite3', 'json', 'datetime']:
                missing.append(package.replace('_', '-'))

    return missing

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات:")
    files = {
        'app.py': 'التطبيق الرئيسي',
        'fixed_app.py': 'التطبيق المُصحح',
        'run_fixed.py': 'ملف التشغيل المُصحح',
        'templates/index.html': 'الواجهة الرئيسية',
        'static/css/style.css': 'ملف التصميم',
        'static/js/app.js': 'ملف JavaScript',
        'requirements.txt': 'متطلبات النظام'
    }

    missing = []
    for file_path, description in files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {description}: {file_path} ({size:,} بايت)")
        else:
            print(f"   ❌ {description}: {file_path}")
            missing.append(file_path)

    return missing

def check_ports():
    """فحص المنافذ"""
    print("\n🔌 فحص المنافذ:")
    ports_to_check = [5000, 5001, 5002, 8000, 8080]

    available_ports = []
    for port in ports_to_check:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()

        if result != 0:
            print(f"   ✅ المنفذ {port} متاح")
            available_ports.append(port)
        else:
            print(f"   ❌ المنفذ {port} مستخدم")

    return available_ports

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قواعد البيانات:")

    db_files = [
        'employees.db',
        'employee_management.db',
        'employees_fixed.db',
        'employees_minimal.db'
    ]

    for db_file in db_files:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            print(f"   ✅ {db_file} ({size:,} بايت)")

            # فحص محتوى قاعدة البيانات
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                print(f"      الجداول: {', '.join([t[0] for t in tables])}")

                if 'employees' in [t[0] for t in tables]:
                    cursor.execute("SELECT COUNT(*) FROM employees")
                    count = cursor.fetchone()[0]
                    print(f"      عدد الموظفين: {count}")

                conn.close()
            except Exception as e:
                print(f"      ⚠️ خطأ في قراءة قاعدة البيانات: {e}")
        else:
            print(f"   ⚠️ {db_file} غير موجودة")

def test_import():
    """اختبار استيراد التطبيق"""
    print("\n🧪 اختبار استيراد التطبيق:")

    test_files = ['app', 'fixed_app', 'run_fixed']

    for module_name in test_files:
        try:
            if os.path.exists(f"{module_name}.py"):
                # محاولة استيراد الوحدة
                spec = __import__(module_name)
                print(f"   ✅ {module_name}.py - تم الاستيراد بنجاح")

                # فحص وجود كائن Flask
                if hasattr(spec, 'app'):
                    print(f"      ✅ يحتوي على كائن Flask")
                else:
                    print(f"      ⚠️ لا يحتوي على كائن Flask")
            else:
                print(f"   ❌ {module_name}.py - غير موجود")

        except Exception as e:
            print(f"   ❌ {module_name}.py - خطأ في الاستيراد: {e}")

def provide_solutions(missing_packages, missing_files, available_ports):
    """تقديم الحلول"""
    print("\n" + "=" * 70)
    print("🔧 الحلول المقترحة:")

    if missing_packages:
        print(f"\n📦 تثبيت المكتبات المفقودة:")
        print(f"   pip install {' '.join(missing_packages)}")
        print(f"   أو: python -m pip install {' '.join(missing_packages)}")

    if missing_files:
        print(f"\n📁 الملفات المفقودة:")
        for file in missing_files:
            print(f"   ❌ {file}")
        print("   💡 تأكد من وجود جميع ملفات النظام")

    if available_ports:
        print(f"\n🚀 طرق التشغيل:")
        print(f"   1. التشغيل المُصحح: python run_fixed.py")
        print(f"   2. التطبيق المُصحح: python fixed_app.py")
        print(f"   3. التطبيق الأصلي: python app.py")
        print(f"   4. ملف Batch: run.bat")
        print(f"   5. تثبيت المتطلبات: python install_requirements.py")
    else:
        print(f"\n⚠️ جميع المنافذ مستخدمة")
        print(f"   💡 أغلق التطبيقات الأخرى أو استخدم منفذ مختلف")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 70)
    print("🔍 تشخيص شامل لنظام إدارة الموظفين")
    print("=" * 70)

    # فحص شامل
    python_ok = check_python()
    pip_ok = check_pip()
    missing_packages = check_packages()
    missing_files = check_files()
    available_ports = check_ports()
    check_database()
    test_import()

    # تقديم الحلول
    provide_solutions(missing_packages, missing_files, available_ports)

    # النتيجة النهائية
    print("\n" + "=" * 70)
    if python_ok and pip_ok and not missing_packages and not missing_files and available_ports:
        print("🎉 التشخيص: النظام جاهز للتشغيل!")
        print("✅ جميع المتطلبات متوفرة")
        print("🚀 يمكنك تشغيل النظام الآن")
    else:
        print("⚠️ التشخيص: هناك مشاكل تحتاج إلى حل")
        if not python_ok:
            print("   ❌ مشكلة في Python")
        if not pip_ok:
            print("   ❌ مشكلة في pip")
        if missing_packages:
            print(f"   ❌ مكتبات مفقودة: {len(missing_packages)}")
        if missing_files:
            print(f"   ❌ ملفات مفقودة: {len(missing_files)}")
        if not available_ports:
            print("   ❌ لا توجد منافذ متاحة")

    print("=" * 70)
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
