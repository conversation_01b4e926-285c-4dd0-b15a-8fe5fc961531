#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشخيص مشاكل النظام
"""

import os
import sys

def diagnose_system():
    print("🔍 تشخيص نظام إدارة الموظفين")
    print("=" * 50)
    
    # فحص Python
    print(f"🐍 إصدار Python: {sys.version}")
    print(f"📁 مجلد العمل: {os.getcwd()}")
    
    # فحص الملفات المطلوبة
    required_files = [
        'app.py',
        'templates/index.html',
        'static/css/style.css',
        'static/js/app.js'
    ]
    
    print("\n📂 فحص الملفات المطلوبة:")
    all_files_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} - موجود ({size} بايت)")
        else:
            print(f"❌ {file_path} - مفقود")
            all_files_exist = False
    
    # فحص المكتبات
    print("\n📦 فحص المكتبات المطلوبة:")
    required_modules = ['flask', 'flask_cors', 'sqlite3', 'json', 'datetime']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - متاح")
        except ImportError:
            print(f"❌ {module} - غير متاح")
            all_files_exist = False
    
    # فحص قاعدة البيانات
    print("\n🗄️ فحص قاعدة البيانات:")
    if os.path.exists('employees.db'):
        size = os.path.getsize('employees.db')
        print(f"✅ employees.db - موجودة ({size} بايت)")
    else:
        print("⚠️ employees.db - غير موجودة (سيتم إنشاؤها تلقائياً)")
    
    # اختبار استيراد التطبيق
    print("\n🚀 اختبار استيراد التطبيق:")
    try:
        sys.path.insert(0, os.getcwd())
        import app
        print("✅ تم استيراد app.py بنجاح")
        
        # اختبار Flask
        if hasattr(app, 'app'):
            print("✅ تم العثور على كائن Flask")
        else:
            print("❌ لم يتم العثور على كائن Flask")
            
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        import traceback
        traceback.print_exc()
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    if all_files_exist:
        print("🎉 التشخيص: جميع الملفات والمكتبات موجودة!")
        print("💡 المشكلة قد تكون في:")
        print("   - إعدادات Python")
        print("   - متغيرات البيئة")
        print("   - صلاحيات الملفات")
        print("   - جدار الحماية")
    else:
        print("⚠️ التشخيص: هناك ملفات أو مكتبات مفقودة")
    
    print("\n🔧 الحلول المقترحة:")
    print("1. تأكد من تثبيت Flask:")
    print("   pip install flask flask-cors")
    print("2. جرب التشغيل المباشر:")
    print("   python app.py")
    print("3. جرب التشغيل البديل:")
    print("   python -m flask --app app run")
    print("4. تحقق من المنفذ:")
    print("   netstat -an | findstr :5000")

if __name__ == '__main__':
    diagnose_system()
