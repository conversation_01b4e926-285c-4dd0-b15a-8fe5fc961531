#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app
    print("تم تحميل التطبيق بنجاح")
    print("بدء تشغيل الخادم على http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
except Exception as e:
    print(f"خطأ في تشغيل التطبيق: {e}")
    import traceback
    traceback.print_exc()
