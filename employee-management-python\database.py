#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين - قاعدة البيانات
Employee Management System - Database Module
"""

import sqlite3
import os
from datetime import datetime
import json

class EmployeeDatabase:
    def __init__(self, db_path="employee_system.db"):
        """تهيئة قاعدة البيانات"""
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def init_database(self):
        """إنشاء الجداول الأساسية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # جدول الموظفين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    company_number TEXT,
                    full_name TEXT,
                    job_title TEXT,
                    work_location TEXT,
                    start_date TEXT,
                    birth_date TEXT,
                    address TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT
                )
            ''')
            
            # جدول الدورات التدريبية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS courses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER,
                    employee_name TEXT,
                    employee_number TEXT,
                    employee_job_title TEXT,
                    course_name TEXT,
                    course_type TEXT,
                    duration TEXT,
                    start_date TEXT,
                    end_date TEXT,
                    location TEXT,
                    status TEXT DEFAULT 'مخططة',
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # جدول الإجازات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS leaves (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER,
                    employee_name TEXT,
                    employee_number TEXT,
                    employee_job_title TEXT,
                    employee_work_location TEXT,
                    leave_type TEXT,
                    start_date TEXT,
                    end_date TEXT,
                    days INTEGER,
                    status TEXT DEFAULT 'معلقة',
                    reason TEXT,
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # جدول الإيفادات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS delegations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER,
                    employee_name TEXT,
                    employee_number TEXT,
                    destination TEXT,
                    purpose TEXT,
                    start_date TEXT,
                    end_date TEXT,
                    type TEXT,
                    status TEXT DEFAULT 'مخطط',
                    cost REAL DEFAULT 0,
                    funding TEXT DEFAULT 'الشركة',
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # جدول أرشفة الكتب
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS books (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    number TEXT,
                    date TEXT,
                    subject TEXT,
                    source TEXT,
                    title TEXT,
                    folder TEXT,
                    attachments TEXT,
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT
                )
            ''')
            
            # جدول الإعدادات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TEXT
                )
            ''')
            
            conn.commit()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def add_employee(self, employee_data):
        """إضافة موظف جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            created_at = datetime.now().isoformat()
            
            cursor.execute('''
                INSERT INTO employees (
                    company_number, full_name, job_title, work_location,
                    start_date, birth_date, address, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_data.get('companyNumber', ''),
                employee_data.get('fullName', ''),
                employee_data.get('jobTitle', ''),
                employee_data.get('workLocation', ''),
                employee_data.get('startDate', ''),
                employee_data.get('birthDate', ''),
                employee_data.get('address', ''),
                created_at
            ))
            
            employee_id = cursor.lastrowid
            conn.commit()
            
            print(f"✅ تم إضافة الموظف بنجاح - ID: {employee_id}")
            return employee_id
            
        except Exception as e:
            print(f"❌ خطأ في إضافة الموظف: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()
    
    def get_all_employees(self):
        """جلب جميع الموظفين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT * FROM employees ORDER BY id DESC')
            employees = cursor.fetchall()
            
            # تحويل النتائج إلى قائمة من القواميس
            result = []
            for i, emp in enumerate(employees, 1):
                result.append({
                    'id': emp['id'],
                    'sequence': i,
                    'companyNumber': emp['company_number'] or 'غير محدد',
                    'fullName': emp['full_name'] or 'غير محدد',
                    'jobTitle': emp['job_title'] or 'غير محدد',
                    'workLocation': emp['work_location'] or 'غير محدد',
                    'startDate': emp['start_date'] or '',
                    'birthDate': emp['birth_date'] or '',
                    'address': emp['address'] or 'غير محدد',
                    'createdAt': emp['created_at']
                })
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في جلب الموظفين: {e}")
            return []
        finally:
            conn.close()
    
    def update_employee(self, employee_id, employee_data):
        """تحديث بيانات موظف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            updated_at = datetime.now().isoformat()
            
            cursor.execute('''
                UPDATE employees SET
                    company_number = ?, full_name = ?, job_title = ?,
                    work_location = ?, start_date = ?, birth_date = ?,
                    address = ?, updated_at = ?
                WHERE id = ?
            ''', (
                employee_data.get('companyNumber', ''),
                employee_data.get('fullName', ''),
                employee_data.get('jobTitle', ''),
                employee_data.get('workLocation', ''),
                employee_data.get('startDate', ''),
                employee_data.get('birthDate', ''),
                employee_data.get('address', ''),
                updated_at,
                employee_id
            ))
            
            conn.commit()
            print(f"✅ تم تحديث الموظف بنجاح - ID: {employee_id}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحديث الموظف: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def delete_employee(self, employee_id):
        """حذف موظف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # حذف البيانات المرتبطة أولاً
            cursor.execute('DELETE FROM courses WHERE employee_id = ?', (employee_id,))
            cursor.execute('DELETE FROM leaves WHERE employee_id = ?', (employee_id,))
            cursor.execute('DELETE FROM delegations WHERE employee_id = ?', (employee_id,))
            
            # حذف الموظف
            cursor.execute('DELETE FROM employees WHERE id = ?', (employee_id,))
            
            conn.commit()
            print(f"✅ تم حذف الموظف بنجاح - ID: {employee_id}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حذف الموظف: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

# إنشاء مثيل من قاعدة البيانات
db = EmployeeDatabase()

if __name__ == "__main__":
    print("🚀 تشغيل نظام قاعدة البيانات...")
    
    # اختبار قاعدة البيانات
    test_employee = {
        'companyNumber': 'EMP001',
        'fullName': 'أحمد محمد علي',
        'jobTitle': 'مطور برمجيات',
        'workLocation': 'الرياض',
        'startDate': '2024-01-15',
        'birthDate': '1990-05-20',
        'address': 'الرياض - حي النرجس'
    }
    
    # إضافة موظف تجريبي
    emp_id = db.add_employee(test_employee)
    
    if emp_id:
        print(f"✅ تم إضافة موظف تجريبي بنجاح - ID: {emp_id}")
        
        # جلب جميع الموظفين
        employees = db.get_all_employees()
        print(f"📊 عدد الموظفين في قاعدة البيانات: {len(employees)}")
        
        for emp in employees:
            print(f"👤 {emp['fullName']} - {emp['companyNumber']}")
