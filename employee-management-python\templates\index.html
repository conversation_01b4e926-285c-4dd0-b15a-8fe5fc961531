<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="rtl-layout" data-theme="light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-people-fill me-2"></i>
                نظام إدارة الموظفين
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-gear-fill"></i>
                        الإعدادات
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showSettings()">
                            <i class="bi bi-sliders me-2"></i>إعدادات النظام
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="toggleTheme()">
                            <i class="bi bi-moon-fill me-2"></i>تبديل الثيم
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid mt-5 pt-3">
        <!-- Welcome Message -->
        <div class="welcome-message text-center mb-4">
            <h1 class="display-4 fw-bold text-primary animated-welcome">
                <i class="bi bi-house-heart-fill me-3"></i>
                مرحباً بك في نظام إدارة الموظفين
            </h1>
            <p class="lead text-muted">إدارة شاملة ومتطورة لبيانات الموظفين والدورات والإجازات</p>
        </div>

        <!-- Navigation Tabs -->
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <ul class="nav nav-tabs card-header-tabs nav-tabs-custom" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active fw-bold" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                            <i class="bi bi-house-fill me-2 text-success"></i>
                            الرئيسية
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="employees-tab" data-bs-toggle="tab" data-bs-target="#employees" type="button" role="tab">
                            <i class="bi bi-people-fill me-2 text-primary"></i>
                            إدارة الموظفين
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="service-tab" data-bs-toggle="tab" data-bs-target="#service" type="button" role="tab">
                            <i class="bi bi-clock-history me-2 text-success"></i>
                            معرفة الخدمة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab">
                            <i class="bi bi-book-fill me-2 text-info"></i>
                            الدورات التدريبية
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="leaves-tab" data-bs-toggle="tab" data-bs-target="#leaves" type="button" role="tab">
                            <i class="bi bi-calendar-check-fill me-2 text-warning"></i>
                            الإجازات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="delegations-tab" data-bs-toggle="tab" data-bs-target="#delegations" type="button" role="tab">
                            <i class="bi bi-airplane-fill me-2 text-danger"></i>
                            الإيفادات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="books-tab" data-bs-toggle="tab" data-bs-target="#books" type="button" role="tab">
                            <i class="bi bi-book-fill me-2 text-purple"></i>
                            أرشفة الكتب
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                            <i class="bi bi-bell-fill me-2 text-secondary"></i>
                            التنبيهات
                            <span class="badge bg-danger ms-1" id="notificationsBadge" style="display: none;">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab">
                            <i class="bi bi-graph-up me-2 text-dark"></i>
                            التقارير
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link fw-bold" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                            <i class="bi bi-gear-fill me-2 text-muted"></i>
                            الإعدادات
                        </button>
                    </li>
                </ul>
            </div>

            <div class="card-body">
                <div class="tab-content" id="mainTabsContent">
                    <!-- Dashboard Tab -->
                    <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4 class="fw-bold text-success">
                                <i class="bi bi-house-fill me-2"></i>
                                لوحة التحكم الرئيسية
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success fw-bold" onclick="refreshDashboard()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>
                                    تحديث البيانات
                                </button>
                                <button type="button" class="btn btn-info fw-bold" onclick="showQuickActions()">
                                    <i class="bi bi-lightning-fill me-2"></i>
                                    إجراءات سريعة
                                </button>
                            </div>
                        </div>

                        <!-- Statistics Cards -->
                        <div class="row mb-4" id="statisticsCards">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card stat-card bg-gradient-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="card-title mb-0">إجمالي الموظفين</h6>
                                                <h2 class="mb-0" id="employeesCount">0</h2>
                                            </div>
                                            <div class="stat-icon">
                                                <i class="bi bi-people-fill"></i>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-white-50">
                                                <i class="bi bi-arrow-up me-1"></i>
                                                العدد الإجمالي للموظفين المسجلين
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent border-0">
                                        <button class="btn btn-outline-light btn-sm fw-bold" onclick="document.getElementById('employees-tab').click()">
                                            <i class="bi bi-eye-fill me-1"></i>
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card stat-card bg-gradient-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="card-title mb-0">الدورات التدريبية</h6>
                                                <h2 class="mb-0" id="coursesCount">0</h2>
                                            </div>
                                            <div class="stat-icon">
                                                <i class="bi bi-book-fill"></i>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-white-50">
                                                <i class="bi bi-graph-up me-1"></i>
                                                إجمالي الدورات المسجلة
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent border-0">
                                        <button class="btn btn-outline-light btn-sm fw-bold" onclick="document.getElementById('courses-tab').click()">
                                            <i class="bi bi-eye-fill me-1"></i>
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card stat-card bg-gradient-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="card-title mb-0">الإجازات</h6>
                                                <h2 class="mb-0" id="leavesCount">0</h2>
                                            </div>
                                            <div class="stat-icon">
                                                <i class="bi bi-calendar-check-fill"></i>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-white-50">
                                                <i class="bi bi-calendar3 me-1"></i>
                                                إجمالي الإجازات المسجلة
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent border-0">
                                        <button class="btn btn-outline-light btn-sm fw-bold" onclick="document.getElementById('leaves-tab').click()">
                                            <i class="bi bi-eye-fill me-1"></i>
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card stat-card bg-gradient-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="card-title mb-0">الإيفادات</h6>
                                                <h2 class="mb-0" id="delegationsCount">0</h2>
                                            </div>
                                            <div class="stat-icon">
                                                <i class="bi bi-airplane-fill"></i>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-white-50">
                                                <i class="bi bi-globe me-1"></i>
                                                إجمالي الإيفادات المسجلة
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent border-0">
                                        <button class="btn btn-outline-light btn-sm fw-bold" onclick="document.getElementById('delegations-tab').click()">
                                            <i class="bi bi-eye-fill me-1"></i>
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-lightning-fill me-2"></i>
                                            إجراءات سريعة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-primary fw-bold" onclick="showAddEmployeeModal()">
                                                <i class="bi bi-person-plus-fill me-2"></i>
                                                إضافة موظف جديد
                                            </button>
                                            <button class="btn btn-outline-success fw-bold" onclick="showAddCourseModal()">
                                                <i class="bi bi-book-fill me-2"></i>
                                                إضافة دورة تدريبية
                                            </button>
                                            <button class="btn btn-outline-warning fw-bold" onclick="showAddLeaveModal()">
                                                <i class="bi bi-calendar-check-fill me-2"></i>
                                                إضافة إجازة
                                            </button>
                                            <button class="btn btn-outline-info fw-bold" onclick="showAddDelegationModal()">
                                                <i class="bi bi-airplane-fill me-2"></i>
                                                إضافة إيفاد
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-graph-up me-2"></i>
                                            ملخص النشاط
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6 mb-3">
                                                <div class="border-end">
                                                    <h4 class="text-primary mb-0" id="dashboardEmployeesCount">0</h4>
                                                    <small class="text-muted">موظف نشط</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <h4 class="text-success mb-0" id="dashboardCoursesCount">0</h4>
                                                <small class="text-muted">دورة مكتملة</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="border-end">
                                                    <h4 class="text-warning mb-0" id="dashboardLeavesCount">0</h4>
                                                    <small class="text-muted">إجازة مُعتمدة</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <h4 class="text-info mb-0" id="dashboardDelegationsCount">0</h4>
                                                <small class="text-muted">إيفاد مُنجز</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-clock-history me-2"></i>
                                            النشاط الأخير
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="timeline" id="recentActivity">
                                            <div class="text-center text-muted">
                                                <i class="bi bi-hourglass-split fs-1"></i>
                                                <p class="mt-2">جاري تحميل النشاط الأخير...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Employees Tab -->
                    <div class="tab-pane fade" id="employees" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-primary">
                                <i class="bi bi-people-fill me-2"></i>
                                إدارة الموظفين
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success fw-bold" onclick="showAddEmployeeModal()">
                                    <i class="bi bi-person-plus-fill me-2"></i>
                                    إضافة موظف جديد
                                </button>
                                <button type="button" class="btn btn-primary fw-bold" onclick="exportEmployees()">
                                    <i class="bi bi-download me-2"></i>
                                    تصدير
                                </button>
                                <button type="button" class="btn btn-info fw-bold" onclick="printEmployees()">
                                    <i class="bi bi-printer-fill me-2"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="employeesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>م</th>
                                        <th>رقم الشركة</th>
                                        <th>الاسم الكامل</th>
                                        <th>المسمى الوظيفي</th>
                                        <th>مكان العمل</th>
                                        <th>تاريخ البداية</th>
                                        <th>عنوان السكن</th>
                                        <th>تاريخ الميلاد</th>
                                        <th>الجوال</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="employeesTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Service Tab -->
                    <div class="tab-pane fade" id="service" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-success">
                                <i class="bi bi-clock-history me-2"></i>
                                معرفة الخدمة
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary fw-bold" onclick="exportService()">
                                    <i class="bi bi-download me-2"></i>
                                    تصدير
                                </button>
                                <button type="button" class="btn btn-info fw-bold" onclick="printService()">
                                    <i class="bi bi-printer-fill me-2"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="serviceTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>م</th>
                                        <th>رقم الشركة</th>
                                        <th>الاسم الكامل</th>
                                        <th>المسمى الوظيفي</th>
                                        <th>مدة الخدمة</th>
                                    </tr>
                                </thead>
                                <tbody id="serviceTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Courses Tab -->
                    <div class="tab-pane fade" id="courses" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-info">
                                <i class="bi bi-book-fill me-2"></i>
                                الدورات التدريبية
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success fw-bold" onclick="showAddCourseModal()">
                                    <i class="bi bi-plus-circle-fill me-2"></i>
                                    إضافة دورة جديدة
                                </button>
                                <button type="button" class="btn btn-primary fw-bold" onclick="exportCourses()">
                                    <i class="bi bi-download me-2"></i>
                                    تصدير
                                </button>
                                <button type="button" class="btn btn-info fw-bold" onclick="printCourses()">
                                    <i class="bi bi-printer-fill me-2"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="coursesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>م</th>
                                        <th>رقم الشركة</th>
                                        <th>الاسم الكامل</th>
                                        <th>المسمى الوظيفي</th>
                                        <th>اسم الدورة</th>
                                        <th>نوع الدورة</th>
                                        <th>المدة</th>
                                        <th>مكان الدورة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="coursesTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Leaves Tab -->
                    <div class="tab-pane fade" id="leaves" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-warning">
                                <i class="bi bi-calendar-check-fill me-2"></i>
                                الإجازات
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success fw-bold" onclick="showAddLeaveModal()">
                                    <i class="bi bi-plus-circle-fill me-2"></i>
                                    إضافة إجازة جديدة
                                </button>
                                <button type="button" class="btn btn-primary fw-bold" onclick="exportLeaves()">
                                    <i class="bi bi-download me-2"></i>
                                    تصدير
                                </button>
                                <button type="button" class="btn btn-info fw-bold" onclick="printLeaves()">
                                    <i class="bi bi-printer-fill me-2"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="leavesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>م</th>
                                        <th>رقم الشركة</th>
                                        <th>الاسم الكامل</th>
                                        <th>المسمى الوظيفي</th>
                                        <th>مكان العمل</th>
                                        <th>تاريخ الإجازة</th>
                                        <th>مدة الإجازة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="leavesTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Delegations Tab -->
                    <div class="tab-pane fade" id="delegations" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-danger">
                                <i class="bi bi-airplane-fill me-2"></i>
                                الإيفادات
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success fw-bold" onclick="showAddDelegationModal()">
                                    <i class="bi bi-plus-circle-fill me-2"></i>
                                    إضافة إيفاد جديد
                                </button>
                                <button type="button" class="btn btn-primary fw-bold" onclick="exportDelegations()">
                                    <i class="bi bi-download me-2"></i>
                                    تصدير
                                </button>
                                <button type="button" class="btn btn-info fw-bold" onclick="printDelegations()">
                                    <i class="bi bi-printer-fill me-2"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="delegationsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>م</th>
                                        <th>رقم الشركة</th>
                                        <th>الاسم الكامل</th>
                                        <th>المسمى الوظيفي</th>
                                        <th>الوجهة</th>
                                        <th>الدولة</th>
                                        <th>الغرض</th>
                                        <th>تاريخ البداية</th>
                                        <th>تاريخ النهاية</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="delegationsTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Books Tab -->
                    <div class="tab-pane fade" id="books" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-purple">
                                <i class="bi bi-book-fill me-2"></i>
                                أرشفة الكتب
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success fw-bold" onclick="showAddBookModal()">
                                    <i class="bi bi-plus-circle-fill me-2"></i>
                                    إضافة كتاب جديد
                                </button>
                                <button type="button" class="btn btn-primary fw-bold" onclick="exportBooks()">
                                    <i class="bi bi-download me-2"></i>
                                    تصدير
                                </button>
                                <button type="button" class="btn btn-info fw-bold" onclick="printBooks()">
                                    <i class="bi bi-printer-fill me-2"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>

                        <!-- Books Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-gradient-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="bi bi-book-fill fs-1 mb-2"></i>
                                        <h4 class="mb-0" id="totalBooksCount">0</h4>
                                        <small>إجمالي الكتب</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-gradient-success text-white">
                                    <div class="card-body text-center">
                                        <i class="bi bi-check-circle-fill fs-1 mb-2"></i>
                                        <h4 class="mb-0" id="availableBooksCount">0</h4>
                                        <small>الكتب المتاحة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-gradient-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="bi bi-arrow-repeat fs-1 mb-2"></i>
                                        <h4 class="mb-0" id="borrowedBooksCount">0</h4>
                                        <small>الكتب المستعارة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-gradient-info text-white">
                                    <div class="card-body text-center">
                                        <i class="bi bi-collection-fill fs-1 mb-2"></i>
                                        <h4 class="mb-0" id="categoriesCount">0</h4>
                                        <small>التصنيفات</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="booksSearch" placeholder="البحث في الكتب...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="علوم">علوم</option>
                                    <option value="أدب">أدب</option>
                                    <option value="تاريخ">تاريخ</option>
                                    <option value="فلسفة">فلسفة</option>
                                    <option value="تقنية">تقنية</option>
                                    <option value="دين">دين</option>
                                    <option value="طب">طب</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="متاح">متاح</option>
                                    <option value="مُستعار">مُستعار</option>
                                    <option value="غير متاح">غير متاح</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearBooksFilters()">
                                    <i class="bi bi-x-circle me-1"></i>
                                    مسح الفلاتر
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="booksTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>م</th>
                                        <th>عنوان الكتاب</th>
                                        <th>المؤلف</th>
                                        <th>التصنيف</th>
                                        <th>الناشر</th>
                                        <th>سنة النشر</th>
                                        <th>عدد الصفحات</th>
                                        <th>اللغة</th>
                                        <th>الموقع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="booksTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Notifications Tab -->
                    <div class="tab-pane fade" id="notifications" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-secondary">
                                <i class="bi bi-bell-fill me-2"></i>
                                التنبيهات
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success fw-bold" onclick="showAddNotificationModal()">
                                    <i class="bi bi-plus-circle-fill me-2"></i>
                                    إضافة تنبيه جديد
                                </button>
                                <button type="button" class="btn btn-warning fw-bold" onclick="markAllNotificationsRead()">
                                    <i class="bi bi-check-all me-2"></i>
                                    تحديد الكل كمقروء
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="notificationsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>م</th>
                                        <th>العنوان</th>
                                        <th>الرسالة</th>
                                        <th>النوع</th>
                                        <th>التاريخ</th>
                                        <th>الموظف</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="notificationsTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Reports Tab -->
                    <div class="tab-pane fade" id="reports" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-dark">
                                <i class="bi bi-graph-up me-2"></i>
                                التقارير والإحصائيات
                            </h4>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-people-fill me-2"></i>
                                            تقرير الموظفين
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">تقرير شامل عن جميع الموظفين وبياناتهم</p>
                                        <button class="btn btn-primary fw-bold" onclick="generateEmployeesReport()">
                                            <i class="bi bi-file-earmark-pdf-fill me-2"></i>
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-clock-history me-2"></i>
                                            تقرير الخدمة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">تقرير مدة خدمة الموظفين</p>
                                        <button class="btn btn-success fw-bold" onclick="generateServiceReport()">
                                            <i class="bi bi-file-earmark-pdf-fill me-2"></i>
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-book-fill me-2"></i>
                                            تقرير الدورات
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">تقرير الدورات التدريبية للموظفين</p>
                                        <button class="btn btn-info fw-bold" onclick="generateCoursesReport()">
                                            <i class="bi bi-file-earmark-pdf-fill me-2"></i>
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-calendar-check-fill me-2"></i>
                                            تقرير الإجازات
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">تقرير إجازات الموظفين</p>
                                        <button class="btn btn-warning fw-bold" onclick="generateLeavesReport()">
                                            <i class="bi bi-file-earmark-pdf-fill me-2"></i>
                                            إنشاء التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Tab -->
                    <div class="tab-pane fade" id="settings" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="fw-bold text-muted">
                                <i class="bi bi-gear-fill me-2"></i>
                                إعدادات النظام
                            </h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success fw-bold" onclick="saveSettings()">
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                    حفظ الإعدادات
                                </button>
                                <button type="button" class="btn btn-warning fw-bold" onclick="resetSettings()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>
                                    إعادة تعيين
                                </button>
                                <button type="button" class="btn btn-info fw-bold" onclick="showUserGuide()">
                                    <i class="bi bi-question-circle-fill me-2"></i>
                                    دليل المستخدم
                                </button>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-palette-fill me-2"></i>
                                            إعدادات المظهر
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">الثيم</label>
                                            <select class="form-select" id="themeSelect">
                                                <option value="light">فاتح</option>
                                                <option value="dark">داكن</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label fw-bold">الخط</label>
                                            <select class="form-select" id="fontSelect">
                                                <option value="tajawal">تجوال</option>
                                                <option value="cairo">القاهرة</option>
                                                <option value="amiri">أميري</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label fw-bold">حجم الخط</label>
                                            <select class="form-select" id="fontSizeSelect">
                                                <option value="small">صغير</option>
                                                <option value="medium">متوسط</option>
                                                <option value="large">كبير</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label fw-bold">لون الثيم</label>
                                            <select class="form-select" id="themeColorSelect">
                                                <option value="default">افتراضي</option>
                                                <option value="blue">أزرق</option>
                                                <option value="green">أخضر</option>
                                                <option value="red">أحمر</option>
                                                <option value="purple">بنفسجي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-bell-fill me-2"></i>
                                            إعدادات التنبيهات
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="notificationsEnabled" checked>
                                            <label class="form-check-label fw-bold" for="notificationsEnabled">
                                                تفعيل التنبيهات
                                            </label>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label fw-bold">مدة عرض التنبيه (بالثواني)</label>
                                            <input type="number" class="form-control" id="notificationDuration" value="5" min="1" max="30">
                                        </div>

                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="employeeNotifications" checked>
                                            <label class="form-check-label fw-bold" for="employeeNotifications">
                                                تنبيهات الموظفين
                                            </label>
                                        </div>

                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="leaveNotifications" checked>
                                            <label class="form-check-label fw-bold" for="leaveNotifications">
                                                تنبيهات الإجازات
                                            </label>
                                        </div>

                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="courseNotifications" checked>
                                            <label class="form-check-label fw-bold" for="courseNotifications">
                                                تنبيهات الدورات
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Employee Modal -->
    <div class="modal fade" id="employeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title fw-bold">
                        <i class="bi bi-person-plus-fill me-2"></i>
                        <span id="employeeModalTitle">إضافة موظف جديد</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="employeeForm">
                        <input type="hidden" id="employeeId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم الشركة</label>
                                <input type="text" class="form-control" id="companyNumber" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الاسم الكامل</label>
                                <input type="text" class="form-control" id="fullName" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المسمى الوظيفي</label>
                                <input type="text" class="form-control" id="jobTitle" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">مكان العمل</label>
                                <input type="text" class="form-control" id="workLocation" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ البداية</label>
                                <input type="date" class="form-control" id="startDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="birthDate">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">عنوان السكن</label>
                                <input type="text" class="form-control" id="address">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم الجوال</label>
                                <input type="tel" class="form-control" id="mobile">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary fw-bold" onclick="saveEmployee()">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Modal -->
    <div class="modal fade" id="courseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title fw-bold">
                        <i class="bi bi-book-fill me-2"></i>
                        إضافة دورة تدريبية
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="courseForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الموظف</label>
                                <select class="form-select" id="courseEmployeeId" required>
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">اسم الدورة</label>
                                <input type="text" class="form-control" id="courseTitle" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">نوع الدورة</label>
                                <select class="form-select" id="courseType" required>
                                    <option value="">اختر النوع</option>
                                    <option value="تقنية">تقنية</option>
                                    <option value="إدارية">إدارية</option>
                                    <option value="مهنية">مهنية</option>
                                    <option value="لغات">لغات</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المدة</label>
                                <input type="text" class="form-control" id="coursePeriod" required placeholder="مثال: 3 أيام">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">مكان الدورة</label>
                                <input type="text" class="form-control" id="courseLocation" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ البداية</label>
                                <input type="date" class="form-control" id="courseStartDate">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-info fw-bold" onclick="saveCourse()">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Modal -->
    <div class="modal fade" id="leaveModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-white">
                    <h5 class="modal-title fw-bold">
                        <i class="bi bi-calendar-check-fill me-2"></i>
                        إضافة إجازة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="leaveForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الموظف</label>
                                <select class="form-select" id="leaveEmployeeId" required>
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">نوع الإجازة</label>
                                <select class="form-select" id="leaveType">
                                    <option value="">اختر النوع</option>
                                    <option value="سنوية">سنوية</option>
                                    <option value="مرضية">مرضية</option>
                                    <option value="طارئة">طارئة</option>
                                    <option value="أمومة">أمومة</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ الإجازة</label>
                                <input type="date" class="form-control" id="leaveDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">مدة الإجازة</label>
                                <input type="text" class="form-control" id="leaveDuration" required placeholder="مثال: 5 أيام">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">سبب الإجازة</label>
                            <textarea class="form-control" id="leaveReason" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning fw-bold" onclick="saveLeave()">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delegation Modal -->
    <div class="modal fade" id="delegationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title fw-bold">
                        <i class="bi bi-airplane-fill me-2"></i>
                        إضافة إيفاد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="delegationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الموظف</label>
                                <select class="form-select" id="delegationEmployeeId" required>
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الوجهة</label>
                                <input type="text" class="form-control" id="delegationDestination" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الدولة</label>
                                <input type="text" class="form-control" id="delegationCountry" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الغرض</label>
                                <input type="text" class="form-control" id="delegationPurpose" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ البداية</label>
                                <input type="date" class="form-control" id="delegationStartDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ النهاية</label>
                                <input type="date" class="form-control" id="delegationEndDate" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger fw-bold" onclick="saveDelegation()">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Book Modal -->
    <div class="modal fade" id="bookModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-purple text-white">
                    <h5 class="modal-title fw-bold">
                        <i class="bi bi-book-fill me-2"></i>
                        <span id="bookModalTitle">إضافة كتاب جديد</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="bookForm">
                        <input type="hidden" id="bookId">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label class="form-label fw-bold">عنوان الكتاب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="bookTitle" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">ISBN</label>
                                <input type="text" class="form-control" id="bookIsbn">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المؤلف</label>
                                <input type="text" class="form-control" id="bookAuthor">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">التصنيف</label>
                                <select class="form-select" id="bookCategory">
                                    <option value="">اختر التصنيف</option>
                                    <option value="علوم">علوم</option>
                                    <option value="أدب">أدب</option>
                                    <option value="تاريخ">تاريخ</option>
                                    <option value="فلسفة">فلسفة</option>
                                    <option value="تقنية">تقنية</option>
                                    <option value="دين">دين</option>
                                    <option value="طب">طب</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الناشر</label>
                                <input type="text" class="form-control" id="bookPublisher">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label fw-bold">سنة النشر</label>
                                <input type="number" class="form-control" id="bookYear" min="1900" max="2030">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label fw-bold">عدد الصفحات</label>
                                <input type="number" class="form-control" id="bookPages" min="1">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">اللغة</label>
                                <select class="form-select" id="bookLanguage">
                                    <option value="العربية">العربية</option>
                                    <option value="الإنجليزية">الإنجليزية</option>
                                    <option value="الفرنسية">الفرنسية</option>
                                    <option value="الألمانية">الألمانية</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">الموقع</label>
                                <input type="text" class="form-control" id="bookLocation" placeholder="مثال: الرف A1">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">الحالة</label>
                                <select class="form-select" id="bookStatus">
                                    <option value="متاح">متاح</option>
                                    <option value="غير متاح">غير متاح</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">الوصف</label>
                            <textarea class="form-control" id="bookDescription" rows="3" placeholder="وصف مختصر عن الكتاب..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-purple fw-bold" onclick="saveBook()">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Borrow Book Modal -->
    <div class="modal fade" id="borrowBookModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-white">
                    <h5 class="modal-title fw-bold">
                        <i class="bi bi-arrow-repeat me-2"></i>
                        استعارة كتاب
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="borrowBookForm">
                        <input type="hidden" id="borrowBookId">
                        <div class="mb-3">
                            <label class="form-label fw-bold">عنوان الكتاب</label>
                            <input type="text" class="form-control" id="borrowBookTitle" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">اسم المستعير <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="borrowerName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">تاريخ الإرجاع المتوقع</label>
                            <input type="date" class="form-control" id="returnDate">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning fw-bold" onclick="confirmBorrowBook()">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        تأكيد الاستعارة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Modal -->
    <div class="modal fade" id="notificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white">
                    <h5 class="modal-title fw-bold">
                        <i class="bi bi-bell-fill me-2"></i>
                        إضافة تنبيه
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="notificationForm">
                        <div class="mb-3">
                            <label class="form-label fw-bold">العنوان</label>
                            <input type="text" class="form-control" id="notificationTitle" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">الرسالة</label>
                            <textarea class="form-control" id="notificationMessage" rows="3" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">النوع</label>
                                <select class="form-select" id="notificationType" required>
                                    <option value="">اختر النوع</option>
                                    <option value="عام">عام</option>
                                    <option value="موظف">موظف</option>
                                    <option value="إجازة">إجازة</option>
                                    <option value="دورة">دورة</option>
                                    <option value="إيفاد">إيفاد</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">التاريخ</label>
                                <input type="date" class="form-control" id="notificationDate" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">الموظف (اختياري)</label>
                            <select class="form-select" id="notificationEmployeeId">
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary fw-bold" onclick="saveNotification()">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>