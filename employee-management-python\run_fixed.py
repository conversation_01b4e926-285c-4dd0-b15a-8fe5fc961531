#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشغيل نظام إدارة الموظفين - حل شامل لجميع المشاكل
"""

import os
import sys
import subprocess
import webbrowser
import threading
import time
import socket

def check_port(port):
    """فحص توفر المنفذ"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    return result != 0

def find_available_port(start_port=5000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        if check_port(port):
            return port
    return None

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 فحص وتثبيت المتطلبات...")
    
    requirements = ['flask', 'flask-cors']
    missing = []
    
    for req in requirements:
        try:
            if req == 'flask-cors':
                import flask_cors
            else:
                __import__(req)
            print(f"✅ {req} - متاح")
        except ImportError:
            missing.append(req)
            print(f"❌ {req} - غير متاح")
    
    if missing:
        print(f"🔧 تثبيت المتطلبات المفقودة...")
        try:
            for req in missing:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
                print(f"✅ تم تثبيت {req}")
        except Exception as e:
            print(f"❌ خطأ في التثبيت: {e}")
            return False
    
    return True

def create_minimal_app():
    """إنشاء تطبيق مبسط"""
    try:
        from flask import Flask, render_template, jsonify
        from flask_cors import CORS
        import sqlite3
        from datetime import datetime
        
        app = Flask(__name__)
        CORS(app)
        
        # إعداد قاعدة البيانات
        def setup_db():
            conn = sqlite3.connect('employees_minimal.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY,
                    company_number TEXT,
                    full_name TEXT,
                    job_title TEXT,
                    work_location TEXT,
                    start_date TEXT,
                    address TEXT,
                    birth_date TEXT,
                    mobile TEXT
                )
            ''')
            
            # إدراج بيانات تجريبية
            cursor.execute('SELECT COUNT(*) FROM employees')
            if cursor.fetchone()[0] == 0:
                employees = [
                    ('EMP001', 'أحمد محمد علي', 'مطور برمجيات', 'الرياض', '2023-01-15', 'الرياض', '1990-05-20', '0501234567'),
                    ('EMP002', 'فاطمة خالد', 'محاسبة', 'جدة', '2023-02-01', 'جدة', '1988-08-12', '0509876543'),
                    ('EMP003', 'محمد عبدالله', 'مدير مشروع', 'الدمام', '2023-03-10', 'الدمام', '1985-12-03', '0551122334')
                ]
                
                cursor.executemany('''
                    INSERT INTO employees (company_number, full_name, job_title, work_location, start_date, address, birth_date, mobile)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', employees)
            
            conn.commit()
            conn.close()
        
        setup_db()
        
        @app.route('/')
        def index():
            return render_template('index.html')
        
        @app.route('/api/statistics')
        def statistics():
            conn = sqlite3.connect('employees_minimal.db')
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM employees')
            count = cursor.fetchone()[0]
            conn.close()
            
            return jsonify({
                'employees_count': count,
                'courses_count': 0,
                'leaves_count': 0,
                'delegations_count': 0,
                'books_count': 0,
                'borrowed_books': 0,
                'unread_notifications': 0
            })
        
        @app.route('/api/employees')
        def employees():
            conn = sqlite3.connect('employees_minimal.db')
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM employees')
            rows = cursor.fetchall()
            conn.close()
            
            result = []
            for i, row in enumerate(rows, 1):
                result.append({
                    'sequence': i,
                    'id': row[0],
                    'company_number': row[1],
                    'full_name': row[2],
                    'job_title': row[3],
                    'work_location': row[4],
                    'start_date': row[5],
                    'address': row[6],
                    'birth_date': row[7],
                    'mobile': row[8]
                })
            
            return jsonify(result)
        
        @app.route('/api/service')
        def service():
            conn = sqlite3.connect('employees_minimal.db')
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM employees')
            rows = cursor.fetchall()
            conn.close()
            
            result = []
            for i, row in enumerate(rows, 1):
                # حساب مدة الخدمة بسيط
                start_date = datetime.fromisoformat(row[5])
                now = datetime.now()
                delta = now - start_date
                years = delta.days // 365
                months = (delta.days % 365) // 30
                
                result.append({
                    'sequence': i,
                    'id': row[0],
                    'company_number': row[1],
                    'full_name': row[2],
                    'job_title': row[3],
                    'service_duration': f"{years} سنة و {months} شهر"
                })
            
            return jsonify(result)
        
        # APIs فارغة للباقي
        @app.route('/api/courses')
        def courses():
            return jsonify([])
        
        @app.route('/api/leaves')
        def leaves():
            return jsonify([])
        
        @app.route('/api/delegations')
        def delegations():
            return jsonify([])
        
        @app.route('/api/books')
        def books():
            return jsonify([])
        
        @app.route('/api/notifications')
        def notifications():
            return jsonify([])
        
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        return None

def open_browser_delayed(url):
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        webbrowser.open(url)
        print(f"🌐 تم فتح المتصفح على {url}")
    except:
        print(f"⚠️ افتح المتصفح يدوياً على: {url}")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 70)
    print("🚀 نظام إدارة الموظفين - حل شامل لجميع المشاكل")
    print("=" * 70)
    
    # فحص Python
    print(f"🐍 Python: {sys.version}")
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # البحث عن منفذ متاح
    port = find_available_port()
    if not port:
        print("❌ لم يتم العثور على منفذ متاح")
        input("اضغط Enter للخروج...")
        return
    
    print(f"🔌 المنفذ المستخدم: {port}")
    
    # إنشاء التطبيق
    app = create_minimal_app()
    if not app:
        print("❌ فشل في إنشاء التطبيق")
        input("اضغط Enter للخروج...")
        return
    
    url = f"http://localhost:{port}"
    print(f"✅ تم إعداد النظام بنجاح")
    print(f"🌐 الخادم متاح على: {url}")
    print(f"📱 سيتم فتح المتصفح تلقائياً...")
    print(f"💡 للإيقاف: اضغط Ctrl+C")
    print("=" * 70)
    
    # فتح المتصفح
    browser_thread = threading.Thread(target=open_browser_delayed, args=(url,))
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        app.run(debug=False, host='127.0.0.1', port=port, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
    finally:
        print("👋 تم إغلاق النظام")

if __name__ == '__main__':
    main()
