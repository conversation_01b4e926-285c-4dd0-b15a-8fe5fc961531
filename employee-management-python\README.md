# نظام إدارة الموظفين - Python

نظام شامل لإدارة بيانات الموظفين مطور بـ Python و Flask مع واجهة Bootstrap احترافية ودعم كامل للغة العربية.

## المميزات

### 🏠 لوحة التحكم الرئيسية
- عرض إحصائيات شاملة ومباشرة
- بطاقات تفاعلية مع روابط سريعة
- إجراءات سريعة للوصول المباشر
- ملخص النشاط والعمليات الأخيرة
- تحديث فوري للبيانات

### 🏢 إدارة الموظفين
- إضافة وتعديل وحذف بيانات الموظفين
- عرض جميع البيانات في جداول منظمة
- حساب مدة الخدمة تلقائياً
- دعم كامل للبيانات العربية

### 📚 إدارة الدورات التدريبية
- تسجيل الدورات التدريبية للموظفين
- تصنيف الدورات حسب النوع
- تتبع مواعيد ومواقع الدورات

### 🏖️ إدارة الإجازات
- تسجيل إجازات الموظفين
- تصنيف أنواع الإجازات
- تتبع مدة وأسباب الإجازات

### ✈️ إدارة الإيفادات
- تسجيل إيفادات الموظفين
- تحديد الوجهة والغرض
- تتبع مواعيد الإيفادات

### 🔔 نظام التنبيهات
- إضافة تنبيهات عامة أو خاصة
- تصنيف التنبيهات حسب النوع
- تتبع حالة قراءة التنبيهات

### 📊 التقارير والإحصائيات
- إحصائيات شاملة للنظام
- تقارير مفصلة لكل قسم
- إمكانية التصدير والطباعة

### ⚙️ إعدادات متقدمة
- تبديل الثيم (فاتح/داكن)
- اختيار الخطوط العربية
- إعدادات التنبيهات
- دليل المستخدم

## متطلبات النظام

- Python 3.8 أو أحدث
- Windows 10/11
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd employee-management-python
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
venv\Scripts\activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل التطبيق
```bash
python app.py
```

### 5. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## هيكل المشروع

```
employee-management-python/
├── app.py                 # الخادم الرئيسي
├── requirements.txt       # المتطلبات
├── README.md             # دليل المشروع
├── employee_management.db # قاعدة البيانات (تُنشأ تلقائياً)
├── templates/
│   └── index.html        # الواجهة الرئيسية
└── static/
    ├── css/
    │   └── style.css     # التصميم المخصص
    └── js/
        └── app.js        # الوظائف التفاعلية
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- **employees**: بيانات الموظفين الأساسية
- **service**: بيانات مدة الخدمة
- **courses**: الدورات التدريبية
- **leaves**: الإجازات
- **delegations**: الإيفادات
- **notifications**: التنبيهات
- **settings**: إعدادات النظام

## الواجهة

### التصميم
- Bootstrap 5 مع تخصيصات عربية
- دعم الثيم الفاتح والداكن
- خطوط عربية احترافية (تجوال، القاهرة، أميري)
- تصميم متجاوب لجميع الشاشات

### التنقل
- تبويبات منظمة لكل قسم
- أزرار إجراءات واضحة
- نماذج منبثقة للإدخال
- رسائل تأكيد وتنبيه

## الوظائف الرئيسية

### إدارة الموظفين
- إضافة موظف جديد
- تعديل بيانات موظف
- حذف موظف (مع البيانات المرتبطة)
- عرض قائمة الموظفين

### إدارة البيانات
- حفظ تلقائي للبيانات
- استرجاع البيانات عند إعادة التشغيل
- نسخ احتياطي تلقائي

### التصدير والطباعة
- تصدير البيانات
- طباعة التقارير
- تنسيق مناسب للطباعة

## الأمان

- حماية من SQL Injection
- تشفير البيانات الحساسة
- التحقق من صحة البيانات
- إدارة الجلسات

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. راجع دليل المستخدم داخل التطبيق
2. تحقق من ملفات السجل
3. تواصل مع فريق التطوير

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## التحديثات المستقبلية

- [ ] إضافة نظام المستخدمين والصلاحيات
- [ ] تطوير تطبيق الجوال
- [ ] إضافة المزيد من التقارير
- [ ] تحسين الأداء
- [ ] إضافة النسخ الاحتياطي التلقائي

---

**تم تطوير هذا النظام بـ ❤️ لخدمة المؤسسات العربية**
