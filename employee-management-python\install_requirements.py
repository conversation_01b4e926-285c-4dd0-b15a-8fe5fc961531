#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تثبيت متطلبات نظام إدارة الموظفين
"""

import sys
import subprocess
import os

def check_python():
    """فحص إصدار Python"""
    print("🐍 فحص Python...")
    print(f"إصدار Python: {sys.version}")
    
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    
    print("✅ إصدار Python مناسب")
    return True

def install_package(package):
    """تثبيت حزمة Python"""
    try:
        print(f"📦 تثبيت {package}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print(f"✅ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل تثبيت {package}: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def check_package(package):
    """فحص وجود حزمة"""
    try:
        if package == 'flask-cors':
            import flask_cors
        else:
            __import__(package)
        return True
    except ImportError:
        return False

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🔧 تثبيت متطلبات نظام إدارة الموظفين")
    print("=" * 60)
    
    # فحص Python
    if not check_python():
        input("اضغط Enter للخروج...")
        return
    
    # قائمة المتطلبات
    requirements = [
        'flask',
        'flask-cors'
    ]
    
    print(f"\n📋 المتطلبات المطلوبة: {', '.join(requirements)}")
    print("\n🔍 فحص المتطلبات الحالية...")
    
    missing_packages = []
    for package in requirements:
        if check_package(package):
            print(f"✅ {package} - متاح")
        else:
            print(f"❌ {package} - غير متاح")
            missing_packages.append(package)
    
    if not missing_packages:
        print("\n🎉 جميع المتطلبات متوفرة!")
        print("✅ يمكنك تشغيل النظام الآن")
    else:
        print(f"\n📦 تثبيت المتطلبات المفقودة: {', '.join(missing_packages)}")
        
        # تحديث pip أولاً
        print("\n🔄 تحديث pip...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
            print("✅ تم تحديث pip")
        except:
            print("⚠️ لم يتم تحديث pip")
        
        # تثبيت المتطلبات
        success_count = 0
        for package in missing_packages:
            if install_package(package):
                success_count += 1
        
        print(f"\n📊 النتائج:")
        print(f"✅ تم تثبيت: {success_count}/{len(missing_packages)}")
        
        if success_count == len(missing_packages):
            print("🎉 تم تثبيت جميع المتطلبات بنجاح!")
            print("✅ يمكنك تشغيل النظام الآن")
        else:
            print("⚠️ لم يتم تثبيت بعض المتطلبات")
            print("💡 جرب التثبيت اليدوي:")
            for package in missing_packages:
                print(f"   pip install {package}")
    
    # فحص الملفات المطلوبة
    print(f"\n📁 فحص الملفات المطلوبة...")
    required_files = [
        'app.py',
        'fixed_app.py',
        'templates/index.html',
        'static/css/style.css',
        'static/js/app.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {len(missing_files)}")
        print("💡 تأكد من وجود جميع ملفات النظام")
    else:
        print("\n✅ جميع الملفات المطلوبة موجودة")
    
    print("\n" + "=" * 60)
    print("🚀 طرق تشغيل النظام:")
    print("1. تشغيل الإصدار المُصحح: python fixed_app.py")
    print("2. تشغيل الإصدار الأصلي: python app.py")
    print("3. تشغيل باستخدام Batch: run.bat")
    print("=" * 60)
    
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
